import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 获取病区下的病人列表
router.get('/wards/:wardId/patients', async (req, res) => {
  const { wardId } = req.params;
  let connection;

  try {
    console.log('获取病人列表请求, wardId:', wardId);
    connection = await oracledb.getConnection();

    // 由于没有 beds 和 patients 表，返回模拟数据
    const mockPatients = [
      {
        id: '1',
        name: '张三',
        bedNumber: '101-1',
        age: 45,
        gender: '男',
        diagnosis: '高血压',
        dietRestriction: '低盐,低脂'
      },
      {
        id: '2',
        name: '李四',
        bedNumber: '101-2',
        age: 32,
        gender: '女',
        diagnosis: '骨折',
        dietRestriction: '高钙'
      }
    ];

    // 根据 wardId 过滤数据
    const filteredPatients = wardId === '1' ? mockPatients :
                            wardId === '2' ? [mockPatients[1]] : [];

    console.log('病人列表数据:', filteredPatients);
    res.json(filteredPatients);

  } catch (error) {
    console.error('获取病人列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('病人列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 获取病人详情
router.get('/:patientId', async (req, res) => {
  const { patientId } = req.params;

  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `SELECT p.id, p.name, w.id as ward_id, w.name as ward_name,
              b.number as bed_number, p.age, p.gender, p.diagnosis,
              d.name as doctor, p.admission_date, p.phone, p.email,
              p.diet_restrictions, p.avatar
       FROM patients p
       JOIN beds b ON p.bed_id = b.id
       JOIN wards w ON b.ward_id = w.id
       JOIN doctors d ON w.doctor_id = d.id
       WHERE p.id = :patientId`,
      { patientId }
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: '病人不存在' });
    }

    const patient = {
      id: result.rows[0][0],
      name: result.rows[0][1],
      wardId: result.rows[0][2],
      wardName: result.rows[0][3],
      bedNumber: result.rows[0][4],
      age: result.rows[0][5],
      gender: result.rows[0][6],
      diagnosis: result.rows[0][7],
      doctor: result.rows[0][8],
      admissionDate: result.rows[0][9],
      phone: result.rows[0][10],
      email: result.rows[0][11],
      dietRestrictions: result.rows[0][12] ? result.rows[0][12].split(',') : [],
      avatar: result.rows[0][13]
    };

    res.json(patient);
  } catch (error) {
    console.error('获取病人详情错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

export default router;
