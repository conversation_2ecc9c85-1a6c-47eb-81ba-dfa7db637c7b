import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import oracledb from 'oracledb';

const router = express.Router();

// 添加 JSON 解析中间件
router.use(express.json());

// 用户登录
router.post('/login', async (req, res) => {
  const { username, password } = req.body;
  let connection;

  try {
    console.log('开始登录验证');
    console.log('请求体:', req.body);
    console.log('用户名:', username, '类型:', typeof username);
    console.log('密码:', password, '类型:', typeof password, '长度:', password?.length);

    // 使用连接池获取连接
    connection = await oracledb.getConnection();
    console.log('数据库连接成功');

    // 查询用户信息
    const result = await connection.execute(
      `SELECT id, username, password, role FROM users WHERE username = :username`,
      { username: username },
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );

    console.log('查询结果:', result.rows);

    if (result.rows.length === 0) {
      console.log('用户不存在:', username);
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    const user = result.rows[0];
    console.log('找到用户:', user.USERNAME);

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.PASSWORD);
    console.log('密码验证结果:', isMatch);

    if (!isMatch) {
      console.log('密码错误');
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.ID, username: user.USERNAME, role: user.ROLE },
      process.env.JWT_SECRET,
      { expiresIn: '8h' }
    );

    console.log('登录成功，生成token');
    res.json({ success: true, token });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ success: false, message: '服务器错误: ' + error.message });
  } finally {
    // 确保连接被关闭
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

export default router;
