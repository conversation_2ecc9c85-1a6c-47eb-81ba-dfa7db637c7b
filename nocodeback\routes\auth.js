import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import oracledb from 'oracledb';

const router = express.Router();

// 添加 JSON 解析中间件
router.use(express.json());

// 用户登录
router.post('/login', async (req, res) => {
  const { username, password } = req.body;
  
  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `SELECT * FROM users WHERE username = :username`,
      [username]
    );
    
    if (result.rows.length === 0) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }
    
    const user = result.rows[0];
    const isMatch = await bcrypt.compare(password, user.password);
    
    if (!isMatch) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }
    
    const token = jwt.sign(
      { userId: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '8h' }
    );
    
    res.json({ success: true, token });
  } catch (error) {
    console.error('登录错误:', error);
    // 打印数据库查询结果 
    // 生成到本地log文件
    


    console.log('查询结果:', result?.rows);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

export default router;
