import oracledb from 'oracledb';
import 'dotenv/config';

// 初始化Oracle客户端
oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function fixTables() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 检查 wards 表结构
    const wardsDesc = await connection.execute(
      `SELECT column_name, data_type FROM user_tab_columns WHERE table_name = 'WARDS' ORDER BY column_id`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('WARDS 表结构:', wardsDesc.rows);
    
    // 检查现有数据
    const wardsData = await connection.execute(
      `SELECT * FROM wards`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('WARDS 表数据:', wardsData.rows);
    
    // 创建 BEDS 表（不使用外键约束）
    try {
      await connection.execute(`DROP TABLE beds CASCADE CONSTRAINTS`);
      console.log('删除现有 BEDS 表');
    } catch (e) {
      console.log('BEDS 表不存在，继续创建');
    }
    
    await connection.execute(`
      CREATE TABLE beds (
        id VARCHAR2(36) PRIMARY KEY,
        number VARCHAR2(20) NOT NULL,
        ward_id VARCHAR2(36)
      )
    `);
    console.log('BEDS 表创建成功');
    
    // 插入床位数据
    await connection.execute(`INSERT INTO beds VALUES ('1', '101-1', '1')`);
    await connection.execute(`INSERT INTO beds VALUES ('2', '101-2', '1')`);
    await connection.execute(`INSERT INTO beds VALUES ('3', '201-1', '2')`);
    await connection.execute(`INSERT INTO beds VALUES ('4', '201-2', '2')`);
    console.log('床位数据插入成功');
    
    // 创建 PATIENTS 表
    try {
      await connection.execute(`DROP TABLE patients CASCADE CONSTRAINTS`);
      console.log('删除现有 PATIENTS 表');
    } catch (e) {
      console.log('PATIENTS 表不存在，继续创建');
    }
    
    await connection.execute(`
      CREATE TABLE patients (
        id VARCHAR2(36) PRIMARY KEY,
        name VARCHAR2(50) NOT NULL,
        bed_id VARCHAR2(36),
        age NUMBER,
        gender VARCHAR2(10),
        diagnosis VARCHAR2(200),
        admission_date DATE,
        phone VARCHAR2(20),
        email VARCHAR2(100),
        diet_restrictions VARCHAR2(200),
        avatar VARCHAR2(200)
      )
    `);
    console.log('PATIENTS 表创建成功');
    
    // 插入病人数据
    await connection.execute(`
      INSERT INTO patients VALUES (
        '1', '张三', '1', 45, '男', '高血压', 
        TO_DATE('2023-01-15', 'YYYY-MM-DD'), '13800138004', '<EMAIL>', 
        '低盐,低脂', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
      )
    `);
    await connection.execute(`
      INSERT INTO patients VALUES (
        '2', '李四', '3', 32, '女', '骨折', 
        TO_DATE('2023-02-20', 'YYYY-MM-DD'), '13800138005', '<EMAIL>', 
        '高钙', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
      )
    `);
    console.log('病人数据插入成功');
    
    // 创建 ORDERS 表
    try {
      await connection.execute(`DROP TABLE orders CASCADE CONSTRAINTS`);
      console.log('删除现有 ORDERS 表');
    } catch (e) {
      console.log('ORDERS 表不存在，继续创建');
    }
    
    await connection.execute(`
      CREATE TABLE orders (
        id VARCHAR2(36) PRIMARY KEY,
        patient_id VARCHAR2(36),
        ward_id VARCHAR2(36),
        status VARCHAR2(20) DEFAULT '待处理',
        total_amount NUMBER(10,2) DEFAULT 0,
        order_date TIMESTAMP DEFAULT SYSTIMESTAMP
      )
    `);
    console.log('ORDERS 表创建成功');
    
    // 创建 ORDER_ITEMS 表
    try {
      await connection.execute(`DROP TABLE order_items CASCADE CONSTRAINTS`);
      console.log('删除现有 ORDER_ITEMS 表');
    } catch (e) {
      console.log('ORDER_ITEMS 表不存在，继续创建');
    }
    
    await connection.execute(`
      CREATE TABLE order_items (
        id VARCHAR2(36) PRIMARY KEY,
        order_id VARCHAR2(36),
        meal_id VARCHAR2(36),
        quantity NUMBER DEFAULT 1,
        price NUMBER(10,2) NOT NULL,
        date DATE NOT NULL,
        meal_type VARCHAR2(20) NOT NULL
      )
    `);
    console.log('ORDER_ITEMS 表创建成功');
    
    // 提交事务
    await connection.commit();
    
    // 测试病区查询
    console.log('测试病区查询...');
    const wardsResult = await connection.execute(
      `SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor 
       FROM wards w
       LEFT JOIN beds b ON w.id = b.ward_id
       LEFT JOIN doctors d ON w.doctor_id = d.id
       GROUP BY w.id, w.name, d.name`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('病区查询结果:', wardsResult.rows);
    
  } catch (error) {
    console.error('修复表失败:', error);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
}

fixTables();
