import { useState } from 'react';
import { Clock, CheckCircle, XCircle, ChevronRight, Home, Utensils, Calendar, User } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

const Orders = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('全部');

  // 使用 API 获取订单列表
  const { data: orders = [], isLoading, error } = useQuery({
    queryKey: ['orders', activeTab],
    queryFn: async () => {
      console.log('获取订单列表, status:', activeTab);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const params = new URLSearchParams();
      if (activeTab && activeTab !== '全部') {
        params.append('status', activeTab);
      }

      const response = await fetch(`/api/orders?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('订单列表响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取订单列表失败');
      }

      const data = await response.json();
      console.log('订单列表数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 数据已经通过 API 过滤，直接使用

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载订单中...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <div className="space-x-4">
            <button
              onClick={() => navigate('/wards')}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              返回病区列表
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题 */}
      <div className="bg-white p-4 border-b sticky top-0 z-10 flex justify-between items-center">
        <Link to="/" className="text-blue-500">
          <Home className="h-6 w-6" />
        </Link>
        <h1 className="text-xl font-bold">我的订单</h1>
        <div className="w-6"></div>
      </div>

      {/* 订单状态筛选 */}
      <div className="bg-white p-4 flex space-x-2 overflow-x-auto">
        {['全部', '已完成', '已取消'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-full text-sm whitespace-nowrap ${
              activeTab === tab
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* 订单列表 */}
      <div className="p-4 space-y-4">
        {orders.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无订单记录
          </div>
        ) : (
          orders.map(order => (
            <div key={order.id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* 订单头部信息 */}
              <div className="p-4 border-b">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm font-medium">{order.date}</span>
                  </div>
                  <div className="flex items-center">
                    {order.status === '已完成' ? (
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    ) : order.status === '已取消' ? (
                      <XCircle className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <Clock className="h-4 w-4 text-orange-500 mr-1" />
                    )}
                    <span className="text-sm">{order.status}</span>
                  </div>
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <Utensils className="h-4 w-4 mr-1" />
                    <span>{order.mealType}</span>
                    <span className="mx-2">|</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{order.deliveryTime}</span>
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>{order.patientName} ({order.bedNumber})</span>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {order.wardName}
                </div>
              </div>

              {/* 订单详情 */}
              <div className="divide-y">
                {order.items.map((item, index) => (
                  <div key={index} className="p-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-12 h-12 rounded object-cover mr-3"
                      />
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">¥{item.price} × {item.quantity}</p>
                      </div>
                    </div>
                    <p className="text-blue-500">¥{item.price * item.quantity}</p>
                  </div>
                ))}
              </div>

              {/* 订单底部 */}
              <div className="p-4 border-t bg-gray-50 flex justify-between items-center">
                <span className="font-medium">总计: ¥{order.total}</span>
                <button className="text-blue-500 text-sm flex items-center">
                  查看详情 <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Orders;
