import { useState } from 'react';
import { Clock, CheckCircle, XCircle, ChevronRight, Home, Utensils, Calendar, User } from 'lucide-react';
import { Link } from 'react-router-dom';

const Orders = () => {
  const orders = [
    {
      id: 1,
      date: '2024-05-20',
      meal: '午餐',
      items: [
        { name: '营养粥套餐', price: 15, quantity: 1, image: 'https://nocode.meituan.com/photo/search?keyword=porridge&width=100&height=100' },
        { name: '低盐低脂餐', price: 25, quantity: 1, image: 'https://nocode.meituan.com/photo/search?keyword=healthy,food&width=100&height=100' }
      ],
      status: '已完成',
      total: 40,
      deliveryTime: '12:30',
      patient: '张伟 (301-3床)'
    },
    {
      id: 2,
      date: '2024-05-19',
      meal: '晚餐',
      items: [
        { name: '流质营养餐', price: 20, quantity: 2, image: 'https://nocode.meituan.com/photo/search?keyword=liquid,food&width=100&height=100' }
      ],
      status: '已取消',
      total: 40,
      deliveryTime: '18:15',
      patient: '张伟 (301-3床)'
    },
    {
      id: 3,
      date: '2024-05-18',
      meal: '早餐',
      items: [
        { name: '米糊套餐', price: 18, quantity: 1, image: 'https://nocode.meituan.com/photo/search?keyword=rice,porridge&width=100&height=100' },
        { name: '蛋花汤套餐', price: 22, quantity: 1, image: 'https://nocode.meituan.com/photo/search?keyword=egg,soup&width=100&height=100' }
      ],
      status: '已完成',
      total: 40,
      deliveryTime: '08:45',
      patient: '张伟 (301-3床)'
    }
  ];

  const [activeTab, setActiveTab] = useState('全部');

  const filteredOrders = activeTab === '全部' 
    ? orders 
    : orders.filter(order => 
        activeTab === '已完成' ? order.status === '已完成' : order.status === '已取消'
      );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题 */}
      <div className="bg-white p-4 border-b sticky top-0 z-10 flex justify-between items-center">
        <Link to="/" className="text-blue-500">
          <Home className="h-6 w-6" />
        </Link>
        <h1 className="text-xl font-bold">我的订单</h1>
        <div className="w-6"></div>
      </div>

      {/* 订单状态筛选 */}
      <div className="bg-white p-4 flex space-x-2 overflow-x-auto">
        {['全部', '已完成', '已取消'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-full text-sm whitespace-nowrap ${
              activeTab === tab 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* 订单列表 */}
      <div className="p-4 space-y-4">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无订单记录
          </div>
        ) : (
          filteredOrders.map(order => (
            <div key={order.id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* 订单头部信息 */}
              <div className="p-4 border-b">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm font-medium">{order.date}</span>
                  </div>
                  <div className="flex items-center">
                    {order.status === '已完成' ? (
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className="text-sm">{order.status}</span>
                  </div>
                </div>
                
                <div className="flex justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <Utensils className="h-4 w-4 mr-1" />
                    <span>{order.meal}</span>
                    <span className="mx-2">|</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{order.deliveryTime}</span>
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>{order.patient}</span>
                  </div>
                </div>
              </div>
              
              {/* 订单详情 */}
              <div className="divide-y">
                {order.items.map((item, index) => (
                  <div key={index} className="p-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <img 
                        src={item.image} 
                        alt={item.name}
                        className="w-12 h-12 rounded object-cover mr-3"
                      />
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">¥{item.price} × {item.quantity}</p>
                      </div>
                    </div>
                    <p className="text-blue-500">¥{item.price * item.quantity}</p>
                  </div>
                ))}
              </div>
              
              {/* 订单底部 */}
              <div className="p-4 border-t bg-gray-50 flex justify-between items-center">
                <span className="font-medium">总计: ¥{order.total}</span>
                <button className="text-blue-500 text-sm flex items-center">
                  查看详情 <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Orders;
