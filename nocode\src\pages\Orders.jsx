import { useState } from 'react';
import { Clock, CheckCircle, XCircle, ChevronRight, Home, Utensils, Calendar, User, ArrowLeft } from 'lucide-react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';

const Orders = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const patientId = searchParams.get('patientId');
  const [activeTab, setActiveTab] = useState('全部');

  // 返回订餐页面的函数
  const handleBackToIndex = () => {
    if (patientId) {
      navigate(`/?patientId=${patientId}`);
    } else {
      navigate('/');
    }
  };

  // 使用 API 获取订单列表
  const { data: orders = [], isLoading, error } = useQuery({
    queryKey: ['orders', activeTab, patientId],
    queryFn: async () => {
      console.log('获取订单列表, status:', activeTab, 'patientId:', patientId);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const params = new URLSearchParams();
      if (activeTab && activeTab !== '全部') {
        params.append('status', activeTab);
      }
      if (patientId) {
        params.append('patientId', patientId);
      }

      const response = await fetch(`/api/orders?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('订单列表响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取订单列表失败');
      }

      const data = await response.json();
      console.log('订单列表数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
  });

  // 退餐 mutation
  const refundMutation = useMutation({
    mutationFn: async ({ orderId, item, patientInfo }) => {
      console.log('提交退餐请求:', { orderId, item, patientInfo });
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      // 构建退餐订单数据（数量为负数）
      const refundData = {
        patientId: patientInfo.patientId,
        patientName: patientInfo.patientName,
        wardName: patientInfo.wardName,
        bedNumber: patientInfo.bedNumber,
        items: [{
          id: item.mealId,
          name: item.name,
          description: '退餐: ' + item.name,
          price: item.price,
          image: item.image,
          category: '退餐',
          quantity: -item.quantity, // 负数表示退餐
          mealDate: new Date().toISOString(), // 退餐日期
          mealTime: '退餐', // 餐次标记为退餐
        }],
        totalPrice: -item.price * item.quantity // 负数表示退款
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(refundData)
      });

      console.log('退餐响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '退餐失败');
      }

      const data = await response.json();
      console.log('退餐成功:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('退餐成功回调:', data);
      alert(`退餐成功！\n退餐记录号: ${data.orderId}`);
      // 重新获取订单列表
      window.location.reload();
    },
    onError: (error) => {
      console.error('退餐失败:', error);
      alert('退餐失败: ' + error.message);
    }
  });

  // 退餐函数
  const handleRefund = (order, item) => {
    if (order.status !== '已完成') {
      alert('只有已完成的订单才能退餐');
      return;
    }

    const confirmRefund = window.confirm(`确认要退订 "${item.name}" 吗？\n退款金额: ¥${item.price * item.quantity}`);

    if (confirmRefund) {
      const patientInfo = {
        patientId: order.patientId,
        patientName: order.patientName,
        wardName: order.wardName,
        bedNumber: order.bedNumber
      };

      refundMutation.mutate({ orderId: order.id, item, patientInfo });
    }
  };

  // 数据已经通过 API 过滤，直接使用

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载订单中...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <div className="space-x-4">
            <button
              onClick={() => navigate('/wards')}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              返回病区列表
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题 */}
      <div className="bg-white p-4 border-b sticky top-0 z-10 flex justify-between items-center">
        <button onClick={handleBackToIndex} className="text-blue-500">
          <ArrowLeft className="h-6 w-6" />
        </button>
        <h1 className="text-xl font-bold">我的订单</h1>
        <div className="w-6"></div>
      </div>

      {/* 订单状态筛选 */}
      <div className="bg-white p-4 flex space-x-2 overflow-x-auto">
        {['全部', '已完成', '已取消'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-full text-sm whitespace-nowrap ${
              activeTab === tab
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* 订单列表 */}
      <div className="p-4 space-y-4">
        {orders.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无订单记录
          </div>
        ) : (
          orders.map(order => (
            <div key={order.id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* 订单头部信息 */}
              <div className="p-4 border-b">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm font-medium">{order.date}</span>
                  </div>
                  <div className="flex items-center">
                    {order.status === '已完成' ? (
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    ) : order.status === '已取消' ? (
                      <XCircle className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <Clock className="h-4 w-4 text-orange-500 mr-1" />
                    )}
                    <span className="text-sm">{order.status}</span>
                  </div>
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <Utensils className="h-4 w-4 mr-1" />
                    <span>{order.mealType}</span>
                    <span className="mx-2">|</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{order.deliveryTime}</span>
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>{order.patientName} ({order.bedNumber})</span>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {order.wardName}
                </div>
              </div>

              {/* 订单详情 */}
              <div className="divide-y">
                {order.items.map((item, index) => (
                  <div key={index} className="p-4 flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-12 h-12 rounded object-cover mr-3"
                      />
                      <div className="flex-1">
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">¥{item.price} × {item.quantity}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <p className="text-blue-500">¥{item.price * item.quantity}</p>
                      {order.status === '已完成' && (
                        <button
                          onClick={() => handleRefund(order, item)}
                          disabled={refundMutation.isPending}
                          className={`px-3 py-1 rounded text-sm transition-colors ${
                            refundMutation.isPending
                              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              : 'bg-red-500 hover:bg-red-600 text-white'
                          }`}
                        >
                          {refundMutation.isPending ? '退餐中...' : '退餐'}
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 订单底部 */}
              <div className="p-4 border-t bg-gray-50 flex justify-between items-center">
                <span className="font-medium">总计: ¥{order.total}</span>
                <button className="text-blue-500 text-sm flex items-center">
                  查看详情 <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Orders;
