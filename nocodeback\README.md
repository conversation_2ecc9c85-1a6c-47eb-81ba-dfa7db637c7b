# 医院营养点餐系统API

## 项目概述
这是一个基于Node.js和Oracle数据库的医院营养点餐系统后端API，为前端应用提供数据支持。

## 功能模块
- 认证管理：用户登录
- 病区管理：获取病区列表和详情
- 病人管理：获取病人列表和详情
- 餐品管理：获取餐品分类和列表
- 订单管理：提交、查询和取消订单
- 统计报表：获取订单统计数据

## 技术栈
- Node.js
- Express
- Oracle数据库
- JWT认证
- Bcrypt密码加密

## 安装与运行
1. 安装依赖：
```bash
npm install
```

2. 配置环境变量：
创建`.env`文件，参考`.env.example`

3. 初始化数据库：
运行`db_init.sql`脚本

4. 启动服务器：
```bash
node server.js
```

## API文档
API文档请参考项目根目录下的`API文档.md`

## 测试账号
- 用户名: admin
- 密码: password
