import { HomeIcon, LogIn, ClipboardList, Users } from "lucide-react";
import Index from "./pages/Index.jsx";
import Login from "./pages/Login.jsx";
import Wards from "./pages/Wards.jsx";
import Patients from "./pages/Patients.jsx";
import Orders from "./pages/Orders.jsx";

export const navItems = [
  {
    title: "首页",
    to: "/",
    icon: <HomeIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "登录",
    to: "/login",
    icon: <LogIn className="h-4 w-4" />,
    page: <Login />,
  },
  {
    title: "病区列表",
    to: "/wards",
    icon: <ClipboardList className="h-4 w-4" />,
    page: <Wards />,
  },
  {
    title: "病人列表",
    to: "/patients/:wardId",
    icon: <Users className="h-4 w-4" />,
    page: <Patients />,
  },
  {
    title: "我的订单",
    to: "/orders",
    icon: <ClipboardList className="h-4 w-4" />,
    page: <Orders />,
  },
];
