import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Home, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

const Wards = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  // Mock数据
  const mockWards = [
    { id: '1', name: '内科一病区', bedCount: 32, doctor: '王医生' },
    { id: '2', name: '外科二病区', bedCount: 28, doctor: '李医生' },
    { id: '3', name: '儿科病区', bedCount: 20, doctor: '张医生' },
    { id: '4', name: '妇产科病区', bedCount: 24, doctor: '刘医生' },
    { id: '5', name: '骨科病区', bedCount: 18, doctor: '陈医生' },
  ];

  const { data: wards = [], isLoading } = useQuery({
    queryKey: ['wards'],
    queryFn: async () => {
      const response = await fetch('/api/wards', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('获取病区列表失败');
      }
      
      return response.json();
    },
  });

  const filteredWards = wards.filter(ward =>
    ward.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white p-4 border-b sticky top-0 z-10">
        <div className="flex justify-between items-center mb-4">
        
          <h1 className="text-xl font-bold">病区列表</h1>
          <div className="w-6"></div>
        </div>
        
        <div className="relative">
          <input
            type="text"
            placeholder="搜索病区..."
            className="w-full p-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-3">
        {filteredWards.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? '没有找到匹配的病区' : '暂无病区数据'}
          </div>
        ) : (
          filteredWards.map(ward => (
            <div 
              key={ward.id}
              onClick={() => navigate(`/patients/${ward.id}`)}
              className="bg-white rounded-lg shadow p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div>
                <h3 className="font-bold text-lg">{ward.name}</h3>
                <p className="text-sm text-gray-600">床位数: {ward.bedCount} | 主治医生: {ward.doctor}</p>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Wards;
