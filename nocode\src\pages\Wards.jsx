import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Home, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

const Wards = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  const { data: wards = [], isLoading, error } = useQuery({
    queryKey: ['wards'],
    queryFn: async () => {
      console.log('发送病区列表请求');
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const response = await fetch('/api/wards', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('病区列表响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取病区列表失败');
      }

      const data = await response.json();
      console.log('病区列表数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  const filteredWards = wards.filter(ward =>
    ward.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载病区列表中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white p-4 border-b sticky top-0 z-10">
        <div className="flex justify-between items-center mb-4">

          <h1 className="text-xl font-bold">病区列表</h1>
          <div className="w-6"></div>
        </div>

        <div className="relative">
          <input
            type="text"
            placeholder="搜索病区..."
            className="w-full p-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-3">
        {filteredWards.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? '没有找到匹配的病区' : '暂无病区数据'}
          </div>
        ) : (
          filteredWards.map(ward => (
            <div
              key={ward.id}
              onClick={() => navigate(`/patients/${ward.id}`)}
              className="bg-white rounded-lg shadow p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div>
                <h3 className="font-bold text-lg">{ward.name}</h3>
                <p className="text-sm text-gray-600">床位数: {ward.bedCount} | 主治医生: {ward.doctor}</p>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Wards;
