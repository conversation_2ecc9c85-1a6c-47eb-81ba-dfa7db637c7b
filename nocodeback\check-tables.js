import oracledb from 'oracledb';
import 'dotenv/config';

// 初始化Oracle客户端
oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function checkTables() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 检查所有表
    const allTables = await connection.execute(
      `SELECT table_name FROM user_tables ORDER BY table_name`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('现有表:', allTables.rows.map(row => row.TABLE_NAME));
    
    // 需要的表
    const requiredTables = ['USERS', 'DOCTORS', 'WARDS', 'NURSES', 'BEDS', 'PATIENTS', 'MEALS', 'ORDERS', 'ORDER_ITEMS'];
    const existingTables = allTables.rows.map(row => row.TABLE_NAME);
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));
    
    console.log('缺少的表:', missingTables);
    
    // 创建缺少的表
    if (missingTables.includes('BEDS')) {
      console.log('创建 BEDS 表...');
      await connection.execute(`
        CREATE TABLE beds (
          id VARCHAR2(36) PRIMARY KEY,
          number VARCHAR2(20) NOT NULL,
          ward_id VARCHAR2(36) REFERENCES wards(id)
        )
      `);
      
      // 插入测试数据
      await connection.execute(`INSERT INTO beds VALUES ('1', '101-1', '1')`);
      await connection.execute(`INSERT INTO beds VALUES ('2', '101-2', '1')`);
      await connection.execute(`INSERT INTO beds VALUES ('3', '201-1', '2')`);
      await connection.execute(`INSERT INTO beds VALUES ('4', '201-2', '2')`);
      console.log('BEDS 表创建成功');
    }
    
    if (missingTables.includes('PATIENTS')) {
      console.log('创建 PATIENTS 表...');
      await connection.execute(`
        CREATE TABLE patients (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          bed_id VARCHAR2(36) REFERENCES beds(id),
          age NUMBER,
          gender VARCHAR2(10),
          diagnosis VARCHAR2(200),
          admission_date DATE,
          phone VARCHAR2(20),
          email VARCHAR2(100),
          diet_restrictions VARCHAR2(200),
          avatar VARCHAR2(200)
        )
      `);
      
      // 插入测试数据
      await connection.execute(`
        INSERT INTO patients VALUES (
          '1', '张三', '1', 45, '男', '高血压', 
          TO_DATE('2023-01-15', 'YYYY-MM-DD'), '13800138004', '<EMAIL>', 
          '低盐,低脂', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
        )
      `);
      await connection.execute(`
        INSERT INTO patients VALUES (
          '2', '李四', '3', 32, '女', '骨折', 
          TO_DATE('2023-02-20', 'YYYY-MM-DD'), '13800138005', '<EMAIL>', 
          '高钙', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
        )
      `);
      console.log('PATIENTS 表创建成功');
    }
    
    if (missingTables.includes('ORDERS')) {
      console.log('创建 ORDERS 表...');
      await connection.execute(`
        CREATE TABLE orders (
          id VARCHAR2(36) PRIMARY KEY,
          patient_id VARCHAR2(36) REFERENCES patients(id),
          ward_id VARCHAR2(36) REFERENCES wards(id),
          status VARCHAR2(20) DEFAULT '待处理',
          total_amount NUMBER(10,2) DEFAULT 0,
          order_date TIMESTAMP DEFAULT SYSTIMESTAMP
        )
      `);
      console.log('ORDERS 表创建成功');
    }
    
    if (missingTables.includes('ORDER_ITEMS')) {
      console.log('创建 ORDER_ITEMS 表...');
      await connection.execute(`
        CREATE TABLE order_items (
          id VARCHAR2(36) PRIMARY KEY,
          order_id VARCHAR2(36) REFERENCES orders(id),
          meal_id VARCHAR2(36) REFERENCES meals(id),
          quantity NUMBER DEFAULT 1,
          price NUMBER(10,2) NOT NULL,
          date DATE NOT NULL,
          meal_type VARCHAR2(20) NOT NULL
        )
      `);
      console.log('ORDER_ITEMS 表创建成功');
    }
    
    // 提交事务
    await connection.commit();
    
    // 测试病区查询
    console.log('测试病区查询...');
    const wardsResult = await connection.execute(
      `SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor 
       FROM wards w
       LEFT JOIN beds b ON w.id = b.ward_id
       LEFT JOIN doctors d ON w.doctor_id = d.id
       GROUP BY w.id, w.name, d.name`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('病区查询结果:', wardsResult.rows);
    
  } catch (error) {
    console.error('检查表失败:', error);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
}

checkTables();
