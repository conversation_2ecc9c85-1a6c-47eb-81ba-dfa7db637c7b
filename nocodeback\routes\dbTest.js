import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 添加一个简单的测试路由
router.get('/ping', (req, res) => {
  console.log('收到 ping 请求');
  res.json({ message: 'pong' });
});

router.get('/test', async (req, res) => {
  console.log('收到数据库测试请求');
  console.log('请求头:', req.headers);
  
  try {
    console.log('尝试连接数据库...');
    // 尝试连接数据库
    const connection = await oracledb.getConnection();
    console.log('数据库连接成功');
    
    // 测试查询
    console.log('执行测试查询...');
    const result = await connection.execute('SELECT 1 FROM DUAL');
    console.log('查询结果:', result.rows);
    
    // 关闭连接
    await connection.close();
    console.log('数据库连接已关闭');
    
    res.json({
      success: true,
      message: '数据库连接成功',
      data: result.rows
    });
  } catch (error) {
    console.error('数据库连接测试错误:', error);
    res.status(500).json({
      success: false,
      message: '数据库连接失败',
      error: error.message
    });
  }
});

export default router; 