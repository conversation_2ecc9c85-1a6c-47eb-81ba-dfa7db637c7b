# 医院营养点餐系统 - 订单表设计

## 订单主表 (meal_orders)

根据医院患者订餐的特殊需求，设计了一个包含完整信息的订单主表，每条记录包含患者基本信息和餐品详细信息。

### 表结构

```sql
CREATE TABLE meal_orders (
  id VARCHAR2(100) PRIMARY KEY,                    -- 订单明细ID（唯一标识）
  order_id VARCHAR2(50) NOT NULL,                 -- 订单批次ID（同一次下单的多个餐品共享）
  
  -- 患者信息
  patient_id VARCHAR2(36) NOT NULL,               -- 患者ID
  patient_name VARCHAR2(100) NOT NULL,            -- 患者姓名
  ward_name VARCHAR2(100) NOT NULL,               -- 病区名称
  bed_number VARCHAR2(20) NOT NULL,               -- 床位号
  
  -- 餐品信息
  meal_id VARCHAR2(36) NOT NULL,                  -- 餐品ID
  meal_name VARCHAR2(200) NOT NULL,               -- 餐品名称
  meal_category VARCHAR2(50) NOT NULL,            -- 餐品分类（普通餐、治疗餐、小炒、流质、半流质）
  meal_type VARCHAR2(20) NOT NULL,                -- 餐次（早餐、午餐、晚餐）
  meal_date DATE NOT NULL,                        -- 用餐日期
  
  -- 订单详情
  quantity NUMBER DEFAULT 1,                      -- 数量
  unit_price NUMBER(10,2) NOT NULL,               -- 单价
  total_amount NUMBER(10,2) NOT NULL,             -- 小计金额
  
  -- 订单状态
  order_date DATE DEFAULT SYSDATE,                -- 下单日期
  order_time TIMESTAMP DEFAULT SYSTIMESTAMP,      -- 下单时间
  status VARCHAR2(20) DEFAULT '待处理',            -- 订单状态（待处理、已完成、已取消）
  
  -- 附加信息
  description VARCHAR2(500),                      -- 餐品描述
  image VARCHAR2(500)                             -- 餐品图片URL
);

-- 创建索引
CREATE INDEX idx_meal_orders_patient ON meal_orders(patient_id);
CREATE INDEX idx_meal_orders_order_id ON meal_orders(order_id);
CREATE INDEX idx_meal_orders_date ON meal_orders(meal_date);
CREATE INDEX idx_meal_orders_status ON meal_orders(status);
```

### 设计特点

1. **完整信息记录**：每条订单记录包含患者的完整信息（姓名、病区、床位）和餐品的完整信息（名称、分类、餐次、日期等）

2. **单表设计**：采用单表设计，避免复杂的关联查询，提高查询效率

3. **批次管理**：通过 `order_id` 字段管理同一次下单的多个餐品

4. **医院特色**：
   - 包含病区和床位信息，便于医院管理
   - 支持餐品分类（治疗餐、流质等医院特有分类）
   - 记录用餐日期和餐次，支持提前订餐

5. **状态管理**：支持订单状态跟踪（待处理、已完成、已取消）

### 数据示例

```json
{
  "id": "1749289675550_101_1749289675550_vy42wtiop",
  "orderId": "1749289675550",
  "patientId": "1",
  "patientName": "张三",
  "wardName": "内科一病区",
  "bedNumber": "101-1",
  "mealId": "101",
  "mealName": "营养粥套餐",
  "mealCategory": "普通餐",
  "mealType": "早餐",
  "mealDate": "2024-12-08",
  "quantity": 1,
  "unitPrice": 15,
  "totalAmount": 15,
  "orderDate": "2024-12-07",
  "orderTime": "2024-12-07T09:47:55.550Z",
  "status": "待处理",
  "description": "健康营养粥品，适合早餐",
  "image": "https://example.com/image.jpg"
}
```

### API接口

#### 创建订单
- **URL**: `POST /api/orders`
- **功能**: 创建新订单，支持一次提交多个餐品
- **请求体**: 包含患者信息和餐品列表
- **响应**: 返回订单ID、订单数量和总金额

#### 查询订单
- **URL**: `GET /api/orders`
- **功能**: 查询订单列表，支持按状态、患者、病区筛选
- **参数**: status（状态）、patientId（患者ID）、wardId（病区ID）
- **响应**: 返回订单列表

这种设计确保了每条订单记录都包含完整的信息，便于医院管理和查询，同时支持复杂的筛选和统计需求。
