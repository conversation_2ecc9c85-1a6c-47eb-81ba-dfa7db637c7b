# 医院营养点餐系统API文档

API URL:http://localhost:5000/api/

## 1. 认证相关接口

### 1.1 用户登录
- **URL**: `/api/auth/login`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "token": "JWT_TOKEN"
  }
  ```

## 2. 病区管理接口

### 2.1 获取所有病区列表
- **URL**: `/api/wards`
- **Method**: GET
- **Response**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "bedCount": number,
      "doctor": "string"
    }
  ]
  ```

### 2.2 获取单个病区详情
- **URL**: `/api/wards/:wardId`
- **Method**: GET
- **Response**:
  ```json
  {
    "id": "string",
    "name": "string",
    "doctor": "string",
    "nurseCount": number
  }
  ```

## 3. 病人管理接口

### 3.1 获取病区下的病人列表
- **URL**: `/api/wards/:wardId/patients`
- **Method**: GET
- **Response**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "bedNumber": "string",
      "age": number,
      "gender": "string",
      "diagnosis": "string",
      "dietRestriction": "string"
    }
  ]
  ```

## 4. 餐品管理接口

### 4.1 获取所有餐品分类
- **URL**: `/api/meal/categories`
- **Method**: GET
- **Response**:
  ```json
  [
    "普通餐",
    "治疗餐",
    "小炒",
    "流质",
    "半流质"
  ]
  ```

### 4.2 获取餐品列表
- **URL**: `/api/meals`
- **Method**: GET
- **Query Parameters**:
  - `category`: 餐品分类
  - `mealType`: 餐次类型
- **Response**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "price": number,
      "image": "string",
      "category": "string",
      "suitableFor": ["string"]
    }
  ]
  ```

## 5. 订单管理接口

### 5.1 提交订单
- **URL**: `/api/orders`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "patientId": "string",
    "wardId": "string",
    "items": [
      {
        "mealId": "string",
        "quantity": number,
        "date": "YYYY-MM-DD",
        "mealType": "string"
      }
    ]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "orderId": "string"
  }
  ```

### 5.2 获取订单列表
- **URL**: `/api/orders`
- **Method**: GET
- **Query Parameters**:
  - `status`: 订单状态
  - `patientId`: 病人ID
  - `wardId`: 病区ID
- **Response**:
  ```json
  [
    {
      "id": "string",
      "patientId": "string",
      "patientName": "string",
      "bedNumber": "string",
      "wardName": "string",
      "date": "YYYY-MM-DD",
      "mealType": "string",
      "items": [
        {
          "mealId": "string",
          "name": "string",
          "price": number,
          "quantity": number,
          "image": "string"
        }
      ],
      "status": "string",
      "total": number,
      "deliveryTime": "HH:mm"
    }
  ]
  ```

## 6. 统计报表接口

### 6.1 获取订单统计数据
- **URL**: `/api/stats/orders`
- **Method**: GET
- **Query Parameters**:
  - `wardId`: 病区ID
  - `patientId`: 病人ID
  - `timeRange`: 时间范围
- **Response**:
  ```json
  {
    "total": number,
    "completed": number,
    "canceled": number,
    "monthlyData": [
      {
        "month": "string",
        "orders": number
      }
    ]
  }
  ```
