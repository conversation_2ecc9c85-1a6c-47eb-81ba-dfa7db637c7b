import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 获取所有餐品分类
router.get('/categories', async (req, res) => {
  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `SELECT DISTINCT category FROM meals`
    );
    
    const categories = result.rows.map(row => row[0]);
    res.json(categories);
  } catch (error) {
    console.error('获取餐品分类错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 获取餐品列表
router.get('/', async (req, res) => {
  const { category, mealType } = req.query;
  
  try {
    const connection = await oracledb.getConnection();
    let query = `SELECT id, name, description, price, image, category, suitable_for 
                 FROM meals WHERE 1=1`;
    const binds = {};
    
    if (category) {
      query += ` AND category = :category`;
      binds.category = category;
    }
    
    if (mealType) {
      query += ` AND meal_type = :mealType`;
      binds.mealType = mealType;
    }
    
    const result = await connection.execute(query, binds);
    
    const meals = result.rows.map(row => ({
      id: row[0],
      name: row[1],
      description: row[2],
      price: row[3],
      image: row[4],
      category: row[5],
      suitableFor: row[6] ? row[6].split(',') : []
    }));
    
    res.json(meals);
  } catch (error) {
    console.error('获取餐品列表错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

export default router;
