import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 获取所有病区列表
router.get('/', async (req, res) => {
  let connection;

  try {
    console.log('获取病区列表请求');
    connection = await oracledb.getConnection();

    const result = await connection.execute(
      `SELECT w.id, w.name, d.name as doctor
       FROM wards w
       LEFT JOIN doctors d ON w.doctor_id = d.id`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );

    console.log('病区查询结果:', result.rows);

    const wards = result.rows.map(row => ({
      id: row.ID,
      name: row.NAME,
      bedCount: row.ID === '1' ? 32 : 28, // 模拟床位数
      doctor: row.DOCTOR || '未分配'
    }));

    console.log('格式化后的病区数据:', wards);
    res.json(wards);

  } catch (error) {
    console.error('获取病区列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('病区列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 获取单个病区详情
router.get('/:wardId', async (req, res) => {
  const { wardId } = req.params;
  let connection;

  try {
    console.log('获取病区详情请求, wardId:', wardId);
    connection = await oracledb.getConnection();

    const result = await connection.execute(
      `SELECT w.id, w.name, d.name as doctor,
              (SELECT COUNT(*) FROM nurses WHERE ward_id = :wardId) as nurse_count
       FROM wards w
       LEFT JOIN doctors d ON w.doctor_id = d.id
       WHERE w.id = :wardId`,
      { wardId },
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );

    console.log('病区详情查询结果:', result.rows);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: '病区不存在' });
    }

    const ward = {
      id: result.rows[0].ID,
      name: result.rows[0].NAME,
      doctor: result.rows[0].DOCTOR || '未分配',
      nurseCount: result.rows[0].NURSE_COUNT || 0
    };

    console.log('格式化后的病区详情:', ward);
    res.json(ward);

  } catch (error) {
    console.error('获取病区详情错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('病区详情查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

export default router;
