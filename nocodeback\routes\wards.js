import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 获取所有病区列表
router.get('/', async (req, res) => {
  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor 
       FROM wards w
       LEFT JOIN beds b ON w.id = b.ward_id
       LEFT JOIN doctors d ON w.doctor_id = d.id
       GROUP BY w.id, w.name, d.name`
    );
    
    const wards = result.rows.map(row => ({
      id: row[0],
      name: row[1],
      bedCount: row[2],
      doctor: row[3]
    }));
    
    res.json(wards);
  } catch (error) {
    console.error('获取病区列表错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 获取单个病区详情
router.get('/:wardId', async (req, res) => {
  const { wardId } = req.params;
  
  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `SELECT w.id, w.name, d.name as doctor, 
              (SELECT COUNT(*) FROM nurses WHERE ward_id = :wardId) as nurse_count
       FROM wards w
       LEFT JOIN doctors d ON w.doctor_id = d.id
       WHERE w.id = :wardId`,
      { wardId }
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ message: '病区不存在' });
    }
    
    const ward = {
      id: result.rows[0][0],
      name: result.rows[0][1],
      doctor: result.rows[0][2],
      nurseCount: result.rows[0][3]
    };
    
    res.json(ward);
  } catch (error) {
    console.error('获取病区详情错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

export default router;
