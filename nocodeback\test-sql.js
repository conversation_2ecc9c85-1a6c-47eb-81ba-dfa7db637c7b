import oracledb from 'oracledb';
import 'dotenv/config';

oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function testSQL() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('Connected to database');
    
    // Test simple query first
    const result1 = await connection.execute('SELECT 1 FROM DUAL');
    console.log('Simple query works:', result1.rows);
    
    // Test table creation with minimal SQL
    try {
      await connection.execute('DROP TABLE test_beds');
    } catch (e) {
      console.log('test_beds table does not exist');
    }
    
    await connection.execute('CREATE TABLE test_beds (id VARCHAR2(10), name VARCHAR2(20))');
    console.log('test_beds table created');
    
    await connection.execute("INSERT INTO test_beds VALUES ('1', 'bed1')");
    console.log('test data inserted');
    
    const result2 = await connection.execute('SELECT * FROM test_beds', {}, { outFormat: oracledb.OUT_FORMAT_OBJECT });
    console.log('test_beds data:', result2.rows);
    
    await connection.commit();
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.close();
      console.log('Connection closed');
    }
  }
}

testSQL();
