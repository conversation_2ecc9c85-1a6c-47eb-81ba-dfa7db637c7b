import oracledb from 'oracledb';

async function updateOrderStatus() {
  let connection;
  
  try {
    connection = await oracledb.getConnection();
    
    // 将第一个订单的状态改为已完成
    const result = await connection.execute(
      `UPDATE meal_orders SET status = '已完成' WHERE order_id = '1749291915893'`,
      {}
    );
    
    await connection.commit();
    
    console.log('订单状态更新成功，影响行数:', result.rowsAffected);
    
  } catch (error) {
    console.error('更新订单状态失败:', error);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
}

updateOrderStatus();
