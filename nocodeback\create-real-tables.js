import oracledb from 'oracledb';
import 'dotenv/config';

oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function createRealTables() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('Connected to database');
    
    // Drop and create beds table
    try {
      await connection.execute('DROP TABLE beds CASCADE CONSTRAINTS');
      console.log('Dropped existing beds table');
    } catch (e) {
      console.log('beds table does not exist');
    }
    
    await connection.execute('CREATE TABLE beds (id VARCHAR2(36) PRIMARY KEY, number VARCHAR2(20) NOT NULL, ward_id VARCHAR2(36))');
    console.log('beds table created');
    
    // Insert bed data
    await connection.execute("INSERT INTO beds VALUES ('1', '101-1', '1')");
    await connection.execute("INSERT INTO beds VALUES ('2', '101-2', '1')");
    await connection.execute("INSERT INTO beds VALUES ('3', '201-1', '2')");
    await connection.execute("INSERT INTO beds VALUES ('4', '201-2', '2')");
    console.log('bed data inserted');
    
    // Drop and create patients table
    try {
      await connection.execute('DROP TABLE patients CASCADE CONSTRAINTS');
      console.log('Dropped existing patients table');
    } catch (e) {
      console.log('patients table does not exist');
    }
    
    await connection.execute('CREATE TABLE patients (id VARCHAR2(36) PRIMARY KEY, name VARCHAR2(50) NOT NULL, bed_id VARCHAR2(36), age NUMBER, gender VARCHAR2(10), diagnosis VARCHAR2(200), admission_date DATE, phone VARCHAR2(20), email VARCHAR2(100), diet_restrictions VARCHAR2(200), avatar VARCHAR2(200))');
    console.log('patients table created');
    
    // Insert patient data
    await connection.execute("INSERT INTO patients VALUES ('1', 'Zhang San', '1', 45, 'Male', 'Hypertension', TO_DATE('2023-01-15', 'YYYY-MM-DD'), '13800138004', '<EMAIL>', 'Low salt, low fat', 'https://example.com/avatar1.jpg')");
    await connection.execute("INSERT INTO patients VALUES ('2', 'Li Si', '3', 32, 'Female', 'Fracture', TO_DATE('2023-02-20', 'YYYY-MM-DD'), '13800138005', '<EMAIL>', 'High calcium', 'https://example.com/avatar2.jpg')");
    console.log('patient data inserted');
    
    // Create orders table
    try {
      await connection.execute('DROP TABLE orders CASCADE CONSTRAINTS');
      console.log('Dropped existing orders table');
    } catch (e) {
      console.log('orders table does not exist');
    }
    
    await connection.execute('CREATE TABLE orders (id VARCHAR2(36) PRIMARY KEY, patient_id VARCHAR2(36), ward_id VARCHAR2(36), status VARCHAR2(20) DEFAULT \'Pending\', total_amount NUMBER(10,2) DEFAULT 0, order_date TIMESTAMP DEFAULT SYSTIMESTAMP)');
    console.log('orders table created');
    
    // Create order_items table
    try {
      await connection.execute('DROP TABLE order_items CASCADE CONSTRAINTS');
      console.log('Dropped existing order_items table');
    } catch (e) {
      console.log('order_items table does not exist');
    }
    
    await connection.execute('CREATE TABLE order_items (id VARCHAR2(36) PRIMARY KEY, order_id VARCHAR2(36), meal_id VARCHAR2(36), quantity NUMBER DEFAULT 1, price NUMBER(10,2) NOT NULL, date DATE NOT NULL, meal_type VARCHAR2(20) NOT NULL)');
    console.log('order_items table created');
    
    await connection.commit();
    console.log('All changes committed');
    
    // Test ward query
    const result = await connection.execute(
      'SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor FROM wards w LEFT JOIN beds b ON w.id = b.ward_id LEFT JOIN doctors d ON w.doctor_id = d.id GROUP BY w.id, w.name, d.name',
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('Ward query result:', result.rows);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.close();
      console.log('Connection closed');
    }
  }
}

createRealTables();
