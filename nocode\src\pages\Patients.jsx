import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Home, ChevronRight, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

const Patients = () => {
  const { wardId } = useParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');

  // Mock数据
  const mockWardInfo = {
    id: wardId,
    name: wardId === '1' ? '内科一病区' : 
          wardId === '2' ? '外科二病区' : 
          wardId === '3' ? '儿科病区' : 
          wardId === '4' ? '妇产科病区' : '骨科病区',
    doctor: wardId === '1' ? '王医生' : 
            wardId === '2' ? '李医生' : 
            wardId === '3' ? '张医生' : 
            wardId === '4' ? '刘医生' : '陈医生',
  };

  const mockPatients = [
    { id: '101', name: '张伟', bedNumber: '301-3', age: 45, gender: '男', diagnosis: '高血压' },
    { id: '102', name: '李娜', bedNumber: '302-1', age: 32, gender: '女', diagnosis: '糖尿病' },
    { id: '103', name: '王强', bedNumber: '303-2', age: 58, gender: '男', diagnosis: '冠心病' },
    { id: '104', name: '赵敏', bedNumber: '304-4', age: 28, gender: '女', diagnosis: '肺炎' },
  ];

  const { data: wardInfo = {}, isLoading: isLoadingWard } = useQuery({
    queryKey: ['ward', wardId],
    queryFn: async () => {
      const response = await fetch(`/api/wards/${wardId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch ward info');
      }
      return response.json();
    },
  });

  const { data: patients = [], isLoading } = useQuery({
    queryKey: ['patients', wardId],
    queryFn: async () => {
      const response = await fetch(`/api/wards/${wardId}/patients`);
      if (!response.ok) {
        throw new Error('Failed to fetch patients');
      }
      return response.json();
    },
  });

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.bedNumber.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoadingWard || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white p-4 border-b sticky top-0 z-10">
        <div className="flex justify-between items-center mb-4">
          <button onClick={() => navigate('/wards')} className="text-blue-500">
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div className="text-center">
            <h1 className="text-xl font-bold">{wardInfo?.name || '病区'}</h1>
            <p className="text-sm text-gray-600">主治医生: {wardInfo?.doctor || '未知'}</p>
          </div>
         
        </div>
        
        <div className="relative">
          <input
            type="text"
            placeholder="搜索病人..."
            className="w-full p-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-3">
        {filteredPatients.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? '没有找到匹配的病人' : '暂无病人数据'}
          </div>
        ) : (
          filteredPatients.map(patient => (
            <div 
              key={patient.id}
              onClick={() => navigate(`/?patientId=${patient.id}`)}
              className="bg-white rounded-lg shadow p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-bold text-lg">{patient.name}</h3>
                  <p className="text-sm text-gray-600">{patient.bedNumber} | {patient.age}岁 | {patient.gender}</p>
                  <p className="text-sm text-gray-600">诊断: {patient.diagnosis}</p>
                </div>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Patients;
