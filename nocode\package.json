{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.0", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-hover-card": "^1.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.48.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.3.9", "html-to-image": "^1.11.11", "input-otp": "^1.2.4", "lucide-react": "^0.417.0", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.52.0", "react-resizable-panels": "^2.0.19", "react-router-dom": "^6.23.1", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@meituan-nocode/vite-plugin-dev-logger": "^0.1.0", "@meituan-nocode/vite-plugin-nocode-html-transformer": "^0.2.4", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "4.3.4", "autoprefixer": "^10.4.19", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "5.4.11"}}