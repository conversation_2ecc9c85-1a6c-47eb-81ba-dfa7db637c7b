import oracledb from 'oracledb';
import 'dotenv/config';

// 初始化Oracle客户端
oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function initDatabase() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 检查表是否存在
    const checkTables = await connection.execute(
      `SELECT table_name FROM user_tables WHERE table_name IN ('USERS', 'DOCTORS', 'WARDS', 'NURSES', 'BEDS', 'PATIENTS', 'MEALS', 'ORDERS', 'ORDER_ITEMS')`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('现有表:', checkTables.rows.map(row => row.TABLE_NAME));
    
    // 如果表不存在，创建它们
    if (checkTables.rows.length === 0) {
      console.log('开始创建数据库表...');
      
      // 创建医生表
      await connection.execute(`
        CREATE TABLE doctors (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          specialty VARCHAR2(100),
          phone VARCHAR2(20)
        )
      `);
      console.log('医生表创建成功');
      
      // 创建病区表
      await connection.execute(`
        CREATE TABLE wards (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          doctor_id VARCHAR2(36) REFERENCES doctors(id)
        )
      `);
      console.log('病区表创建成功');
      
      // 创建护士表
      await connection.execute(`
        CREATE TABLE nurses (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          ward_id VARCHAR2(36) REFERENCES wards(id),
          phone VARCHAR2(20)
        )
      `);
      console.log('护士表创建成功');
      
      // 创建病床表
      await connection.execute(`
        CREATE TABLE beds (
          id VARCHAR2(36) PRIMARY KEY,
          number VARCHAR2(20) NOT NULL,
          ward_id VARCHAR2(36) REFERENCES wards(id)
        )
      `);
      console.log('病床表创建成功');
      
      // 创建病人表
      await connection.execute(`
        CREATE TABLE patients (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          bed_id VARCHAR2(36) REFERENCES beds(id),
          age NUMBER,
          gender VARCHAR2(10),
          diagnosis VARCHAR2(200),
          admission_date DATE,
          phone VARCHAR2(20),
          email VARCHAR2(100),
          diet_restrictions VARCHAR2(200),
          avatar VARCHAR2(200)
        )
      `);
      console.log('病人表创建成功');
      
      // 创建餐品表
      await connection.execute(`
        CREATE TABLE meals (
          id VARCHAR2(36) PRIMARY KEY,
          name VARCHAR2(50) NOT NULL,
          description VARCHAR2(200),
          price NUMBER(10,2) NOT NULL,
          image VARCHAR2(200),
          category VARCHAR2(50) NOT NULL,
          meal_type VARCHAR2(20),
          suitable_for VARCHAR2(200)
        )
      `);
      console.log('餐品表创建成功');
      
      // 创建订单表
      await connection.execute(`
        CREATE TABLE orders (
          id VARCHAR2(36) PRIMARY KEY,
          patient_id VARCHAR2(36) REFERENCES patients(id),
          ward_id VARCHAR2(36) REFERENCES wards(id),
          status VARCHAR2(20) DEFAULT '待处理',
          total_amount NUMBER(10,2) DEFAULT 0,
          order_date TIMESTAMP DEFAULT SYSTIMESTAMP
        )
      `);
      console.log('订单表创建成功');
      
      // 创建订单明细表
      await connection.execute(`
        CREATE TABLE order_items (
          id VARCHAR2(36) PRIMARY KEY,
          order_id VARCHAR2(36) REFERENCES orders(id),
          meal_id VARCHAR2(36) REFERENCES meals(id),
          quantity NUMBER DEFAULT 1,
          price NUMBER(10,2) NOT NULL,
          date DATE NOT NULL,
          meal_type VARCHAR2(20) NOT NULL
        )
      `);
      console.log('订单明细表创建成功');
      
      // 插入测试数据
      console.log('开始插入测试数据...');
      
      // 插入医生数据
      await connection.execute(`
        INSERT INTO doctors VALUES ('1', '张医生', '内科', '13800138000')
      `);
      await connection.execute(`
        INSERT INTO doctors VALUES ('2', '李医生', '外科', '13800138001')
      `);
      
      // 插入病区数据
      await connection.execute(`
        INSERT INTO wards VALUES ('1', '内科一病区', '1')
      `);
      await connection.execute(`
        INSERT INTO wards VALUES ('2', '外科一病区', '2')
      `);
      
      // 插入护士数据
      await connection.execute(`
        INSERT INTO nurses VALUES ('1', '王护士', '1', '13800138002')
      `);
      await connection.execute(`
        INSERT INTO nurses VALUES ('2', '赵护士', '2', '13800138003')
      `);
      
      // 插入病床数据
      await connection.execute(`
        INSERT INTO beds VALUES ('1', '101-1', '1')
      `);
      await connection.execute(`
        INSERT INTO beds VALUES ('2', '101-2', '1')
      `);
      await connection.execute(`
        INSERT INTO beds VALUES ('3', '201-1', '2')
      `);
      await connection.execute(`
        INSERT INTO beds VALUES ('4', '201-2', '2')
      `);
      
      // 插入病人数据
      await connection.execute(`
        INSERT INTO patients VALUES (
          '1', '张三', '1', 45, '男', '高血压', 
          TO_DATE('2023-01-15', 'YYYY-MM-DD'), '13800138004', '<EMAIL>', 
          '低盐,低脂', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
        )
      `);
      await connection.execute(`
        INSERT INTO patients VALUES (
          '2', '李四', '3', 32, '女', '骨折', 
          TO_DATE('2023-02-20', 'YYYY-MM-DD'), '13800138005', '<EMAIL>', 
          '高钙', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
        )
      `);
      
      // 插入餐品数据
      await connection.execute(`
        INSERT INTO meals VALUES (
          '1', '清蒸鱼', '新鲜草鱼清蒸', 25.00, 
          'https://nocode.meituan.com/photo/search?keyword=fish&width=400&height=300', 
          '普通餐', '午餐', '高血压,糖尿病'
        )
      `);
      await connection.execute(`
        INSERT INTO meals VALUES (
          '2', '排骨汤', '猪骨熬制', 18.00, 
          'https://nocode.meituan.com/photo/search?keyword=soup&width=400&height=300', 
          '普通餐', '晚餐', '骨折,术后'
        )
      `);
      
      // 提交事务
      await connection.commit();
      console.log('测试数据插入成功');
      
    } else {
      console.log('数据库表已存在，跳过创建');
    }
    
    // 验证数据
    const wardsResult = await connection.execute(
      `SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor 
       FROM wards w
       LEFT JOIN beds b ON w.id = b.ward_id
       LEFT JOIN doctors d ON w.doctor_id = d.id
       GROUP BY w.id, w.name, d.name`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('病区数据验证:', wardsResult.rows);
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
}

initDatabase();
