import { useState } from 'react';
import { <PERSON>r, <PERSON><PERSON>s, CreditCard, LogOut, ClipboardList, Bed, Calendar, Phone, Mail, ShoppingCart, Clock, CheckCircle, XCircle } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const Profile = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('个人信息');

  const patientInfo = {
    name: '张伟',
    ward: '内科一病区',
    bedNumber: '301-3床',
    admissionDate: '2024-05-10',
    diagnosis: '高血压',
    doctor: '王医生',
    phone: '13800138000',
    email: '<EMAIL>',
    avatar: 'https://nocode.meituan.com/photo/search?keyword=patient,avatar&width=200&height=200'
  };

  const orderStats = {
    total: 24,
    completed: 18,
    canceled: 6,
    monthlyData: [
      { month: '1月', orders: 2 },
      { month: '2月', orders: 3 },
      { month: '3月', orders: 4 },
      { month: '4月', orders: 5 },
      { month: '5月', orders: 10 }
    ]
  };

  const handleLogout = () => {
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部个人信息 */}
      <div className="bg-blue-500 p-6 text-white">
        <div className="flex items-center">
          <img 
            src={patientInfo.avatar} 
            alt={patientInfo.name}
            className="w-20 h-20 rounded-full object-cover border-2 border-white"
          />
          <div className="ml-4">
            <h2 className="text-xl font-bold">{patientInfo.name}</h2>
            <p className="text-sm opacity-80">{patientInfo.ward} {patientInfo.bedNumber}</p>
          </div>
        </div>
      </div>

      {/* 选项卡导航 */}
      <div className="bg-white p-4 flex space-x-2 overflow-x-auto">
        {['个人信息', '订单统计'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-full text-sm whitespace-nowrap ${
              activeTab === tab 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 text-gray-700'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4">
        {activeTab === '个人信息' ? (
          <>
            {/* 患者详细信息 */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-bold text-lg mb-3">患者信息</h3>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <ClipboardList className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">诊断</p>
                    <p>{patientInfo.diagnosis}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">主治医生</p>
                    <p>{patientInfo.doctor}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Bed className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">病区床号</p>
                    <p>{patientInfo.ward} {patientInfo.bedNumber}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">入院日期</p>
                    <p>{patientInfo.admissionDate}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">联系电话</p>
                    <p>{patientInfo.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">电子邮箱</p>
                    <p>{patientInfo.email}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 快捷功能 */}
            <div className="bg-white rounded-lg shadow">
              <Link to="/orders" className="flex items-center p-4 border-b">
                <ShoppingCart className="h-5 w-5 text-gray-500 mr-3" />
                <span>我的订单</span>
              </Link>
              <Link to="/settings" className="flex items-center p-4 border-b">
                <Settings className="h-5 w-5 text-gray-500 mr-3" />
                <span>账户设置</span>
              </Link>
              <button 
                onClick={handleLogout}
                className="flex items-center p-4 w-full text-left text-red-500"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span>退出登录</span>
              </button>
            </div>
          </>
        ) : (
          <div className="space-y-4">
            {/* 订单统计卡片 */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-bold text-lg mb-4">订单概览</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-gray-500">总订单</p>
                  <p className="text-xl font-bold">{orderStats.total}</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-gray-500">已完成</p>
                  <p className="text-xl font-bold text-green-500">{orderStats.completed}</p>
                </div>
                <div className="p-3 bg-red-50 rounded-lg">
                  <p className="text-sm text-gray-500">已取消</p>
                  <p className="text-xl font-bold text-red-500">{orderStats.canceled}</p>
                </div>
              </div>
            </div>

            {/* 订单趋势图 */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-bold text-lg mb-4">月度订单趋势</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={orderStats.monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="orders" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* 最近订单 */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-bold text-lg mb-4">最近订单</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>营养早餐套餐</span>
                  </div>
                  <div className="text-sm text-gray-500">2024-05-20</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span>低盐低脂午餐</span>
                  </div>
                  <div className="text-sm text-gray-500">2024-05-19</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                    <span>流质营养晚餐</span>
                  </div>
                  <div className="text-sm text-gray-500">2024-05-18</div>
                </div>
              </div>
              <div className="mt-4 text-center">
                <Link to="/orders" className="text-blue-500 text-sm">查看全部订单 →</Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
