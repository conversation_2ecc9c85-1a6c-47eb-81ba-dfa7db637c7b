import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 获取订单列表
router.get('/', async (req, res) => {
  const { status, patientId, wardId } = req.query;
  let connection;

  try {
    console.log('获取订单列表请求, status:', status, 'patientId:', patientId, 'wardId:', wardId);
    connection = await oracledb.getConnection();

    // 从数据库查询订单数据
    let orders = [];

    try {
      // 先尝试从数据库查询
      let query = `
        SELECT
          id, order_id, patient_id, patient_name, ward_name, bed_number,
          meal_id, meal_name, meal_category, meal_type, meal_date,
          quantity, unit_price, total_amount, order_date, order_time,
          status, description, image
        FROM meal_orders
        WHERE 1=1
      `;

      const binds = {};

      if (status && status !== '全部') {
        query += ` AND status = :status`;
        binds.status = status;
      }

      if (patientId) {
        query += ` AND patient_id = :patientId`;
        binds.patientId = patientId;
      }

      if (wardId) {
        query += ` AND ward_name LIKE :wardName`;
        binds.wardName = `%病区%`; // 简化处理，根据实际情况调整
      }

      query += ` ORDER BY order_time DESC`;

      console.log('执行查询:', query);
      console.log('查询参数:', binds);

      const result = await connection.execute(query, binds, {
        outFormat: oracledb.OUT_FORMAT_OBJECT
      });

      console.log('数据库查询结果:', result.rows.length, '条记录');

      // 将数据库结果转换为前端需要的格式
      // 按餐品聚合，计算每个餐品的净数量（正数订单 - 退餐数量）
      const mealItemsMap = new Map();

      result.rows.forEach(row => {
        const mealKey = `${row.PATIENT_ID}_${row.MEAL_ID}_${row.MEAL_TYPE}_${row.MEAL_DATE ? row.MEAL_DATE.toISOString().split('T')[0] : ''}`;

        if (!mealItemsMap.has(mealKey)) {
          mealItemsMap.set(mealKey, {
            patientId: row.PATIENT_ID,
            patientName: row.PATIENT_NAME,
            bedNumber: row.BED_NUMBER,
            wardName: row.WARD_NAME,
            mealId: row.MEAL_ID,
            mealName: row.MEAL_NAME,
            mealType: row.MEAL_TYPE,
            mealDate: row.MEAL_DATE ? row.MEAL_DATE.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            unitPrice: row.UNIT_PRICE,
            image: row.IMAGE || 'https://nocode.meituan.com/photo/search?keyword=food&width=100&height=100',
            totalQuantity: 0,
            totalAmount: 0,
            orderIds: new Set(),
            latestOrderTime: row.ORDER_TIME,
            status: row.STATUS
          });
        }

        const mealItem = mealItemsMap.get(mealKey);
        mealItem.totalQuantity += row.QUANTITY;
        mealItem.totalAmount += row.TOTAL_AMOUNT;
        mealItem.orderIds.add(row.ORDER_ID);

        // 保留最新的订单时间和状态
        if (row.ORDER_TIME > mealItem.latestOrderTime) {
          mealItem.latestOrderTime = row.ORDER_TIME;
          mealItem.status = row.STATUS;
        }
      });

      // 只保留总数量大于0的餐品记录
      const validMealItems = Array.from(mealItemsMap.values()).filter(item => item.totalQuantity > 0);

      // 按日期和餐次重新组织为订单格式
      const ordersMap = new Map();

      validMealItems.forEach(mealItem => {
        const orderKey = `${mealItem.patientId}_${mealItem.mealDate}_${mealItem.mealType}`;

        if (!ordersMap.has(orderKey)) {
          ordersMap.set(orderKey, {
            id: Array.from(mealItem.orderIds)[0], // 使用第一个订单ID作为显示ID
            patientId: mealItem.patientId,
            patientName: mealItem.patientName,
            bedNumber: mealItem.bedNumber,
            wardName: mealItem.wardName,
            date: mealItem.mealDate,
            mealType: mealItem.mealType,
            items: [],
            status: mealItem.status,
            total: 0,
            deliveryTime: '12:00' // 默认送餐时间
          });
        }

        const order = ordersMap.get(orderKey);
        order.items.push({
          mealId: mealItem.mealId,
          name: mealItem.mealName,
          price: mealItem.unitPrice,
          quantity: mealItem.totalQuantity, // 使用净数量
          image: mealItem.image
        });

        order.total += mealItem.totalAmount;
      });

      orders = Array.from(ordersMap.values());
      console.log('数据库查询成功，聚合后返回', orders.length, '个有效订单');

    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      // 数据库查询失败时返回空数组
      orders = [];
    }

    // 根据参数过滤数据
    let filteredOrders = orders;

    if (status && status !== '全部') {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    if (patientId) {
      filteredOrders = filteredOrders.filter(order => order.patientId === patientId);
    }

    if (wardId) {
      // 根据病区过滤（简化处理）
      if (wardId === '1') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '张三');
      } else if (wardId === '2') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '李四');
      }
    }

    console.log('过滤后的订单数据:', filteredOrders.length, '个订单');
    res.json(filteredOrders);

  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('订单列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 创建新订单
router.post('/', async (req, res) => {
  const { patientId, patientName, wardName, bedNumber, items, totalPrice } = req.body;
  let connection;

  try {
    console.log('创建订单请求:', req.body);
    connection = await oracledb.getConnection();

    // 生成订单ID
    const orderId = Date.now().toString();
    const orderDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    // 为每个餐品创建订单记录
    const orderRecords = [];

    for (const item of items) {
      const orderRecord = {
        id: `${orderId}_${item.id}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        orderId: orderId,
        patientId: patientId,
        patientName: patientName,
        wardName: wardName,
        bedNumber: bedNumber,
        mealId: item.id,
        mealName: item.name,
        mealCategory: item.category,
        mealType: item.mealTime, // 餐次（早餐、午餐、晚餐）
        mealDate: item.mealDate.split('T')[0], // 用餐日期
        quantity: item.quantity,
        unitPrice: item.price,
        totalAmount: item.price * item.quantity,
        orderDate: orderDate,
        orderTime: new Date().toISOString(),
        status: '待处理',
        description: item.description || '',
        image: item.image || ''
      };

      orderRecords.push(orderRecord);
    }

    console.log('订单记录:', orderRecords);

    // 将订单记录保存到数据库
    try {
      for (const record of orderRecords) {
        await connection.execute(
          `INSERT INTO meal_orders (
            id, order_id, patient_id, patient_name, ward_name, bed_number,
            meal_id, meal_name, meal_category, meal_type, meal_date,
            quantity, unit_price, total_amount, order_date, order_time,
            status, description, image
          ) VALUES (
            :id, :orderId, :patientId, :patientName, :wardName, :bedNumber,
            :mealId, :mealName, :mealCategory, :mealType, TO_DATE(:mealDate, 'YYYY-MM-DD'),
            :quantity, :unitPrice, :totalAmount, TO_DATE(:orderDate, 'YYYY-MM-DD'),
            TO_TIMESTAMP(:orderTime, 'YYYY-MM-DD"T"HH24:MI:SS.FF3"Z"'),
            :status, :description, :image
          )`,
          record
        );
      }

      await connection.commit();
      console.log('订单保存成功（数据库），订单ID:', orderId);

    } catch (dbError) {
      console.error('数据库保存失败:', dbError);
      await connection.rollback();
      throw new Error('订单保存失败: ' + dbError.message);
    }

    res.json({
      success: true,
      message: '订单提交成功',
      orderId: orderId,
      orderCount: orderRecords.length,
      totalAmount: totalPrice
    });

  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      success: false,
      message: '订单提交失败: ' + error.message
    });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('创建订单 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 取消订单
router.post('/:orderId/cancel', async (req, res) => {
  const { orderId } = req.params;
  let connection;

  try {
    connection = await oracledb.getConnection();

    // 尝试更新数据库中的订单状态
    try {
      const result = await connection.execute(
        `UPDATE meal_orders SET status = '已取消' WHERE order_id = :orderId AND status = '待处理'`,
        { orderId }
      );

      await connection.commit();

      if (result.rowsAffected === 0) {
        return res.status(400).json({ success: false, message: '订单无法取消或不存在' });
      }

      console.log('订单取消成功（数据库）:', orderId);

    } catch (dbError) {
      console.error('数据库更新失败:', dbError);
      await connection.rollback();
      return res.status(500).json({ success: false, message: '取消订单失败: ' + dbError.message });
    }

    res.json({ success: true, message: '订单已取消' });

  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('取消订单 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

export default router;
