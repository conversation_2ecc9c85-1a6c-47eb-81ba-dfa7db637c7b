import express from 'express';
import oracledb from 'oracledb';
import dayjs from 'dayjs';

const router = express.Router();

// 获取订单列表
router.get('/', async (req, res) => {
  const { status, patientId, wardId } = req.query;
  let connection;

  try {
    console.log('获取订单列表请求, status:', status, 'patientId:', patientId, 'wardId:', wardId);
    connection = await oracledb.getConnection();

    // 由于没有完整的数据库表，返回模拟数据
    const mockOrders = [
      {
        id: '1',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-07',
        mealType: '午餐',
        items: [
          {
            mealId: '201',
            name: '清蒸鱼',
            price: 28,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=fish&width=100&height=100'
          },
          {
            mealId: '101',
            name: '营养粥套餐',
            price: 15,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=porridge&width=100&height=100'
          }
        ],
        status: '已完成',
        total: 43,
        deliveryTime: '12:30'
      },
      {
        id: '2',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-06',
        mealType: '晚餐',
        items: [
          {
            mealId: '301',
            name: '米糊套餐',
            price: 18,
            quantity: 2,
            image: 'https://nocode.meituan.com/photo/search?keyword=rice,porridge&width=100&height=100'
          }
        ],
        status: '已取消',
        total: 36,
        deliveryTime: '18:15'
      },
      {
        id: '3',
        patientId: '2',
        patientName: '李四',
        bedNumber: '201-1',
        wardName: '外科一病区',
        date: '2024-12-05',
        mealType: '早餐',
        items: [
          {
            mealId: '102',
            name: '低盐低脂餐',
            price: 25,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=healthy,food&width=100&height=100'
          }
        ],
        status: '已完成',
        total: 25,
        deliveryTime: '08:45'
      },
      {
        id: '4',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-04',
        mealType: '午餐',
        items: [
          {
            mealId: '202',
            name: '流质营养餐',
            price: 20,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=liquid,food&width=100&height=100'
          }
        ],
        status: '待处理',
        total: 20,
        deliveryTime: '12:00'
      }
    ];

    // 根据参数过滤数据
    let filteredOrders = mockOrders;

    if (status && status !== '全部') {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    if (patientId) {
      filteredOrders = filteredOrders.filter(order => order.patientId === patientId);
    }

    if (wardId) {
      // 根据病区过滤（简化处理）
      if (wardId === '1') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '张三');
      } else if (wardId === '2') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '李四');
      }
    }

    console.log('过滤后的订单数据:', filteredOrders);
    res.json(filteredOrders);

  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('订单列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 创建新订单
router.post('/', async (req, res) => {
  const { patientId, patientName, wardName, bedNumber, items, totalPrice } = req.body;
  let connection;

  try {
    console.log('创建订单请求:', req.body);
    connection = await oracledb.getConnection();

    // 生成订单ID
    const orderId = Date.now().toString();
    const orderDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    // 为每个餐品创建订单记录
    const orderRecords = [];

    for (const item of items) {
      const orderRecord = {
        id: `${orderId}_${item.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        orderId: orderId,
        patientId: patientId,
        patientName: patientName,
        wardName: wardName,
        bedNumber: bedNumber,
        mealId: item.id,
        mealName: item.name,
        mealCategory: item.category,
        mealType: item.mealTime, // 餐次（早餐、午餐、晚餐）
        mealDate: item.mealDate.split('T')[0], // 用餐日期
        quantity: item.quantity,
        unitPrice: item.price,
        totalAmount: item.price * item.quantity,
        orderDate: orderDate,
        orderTime: new Date().toISOString(),
        status: '待处理',
        description: item.description || '',
        image: item.image || ''
      };

      orderRecords.push(orderRecord);
    }

    console.log('订单记录:', orderRecords);

    // 由于没有真实的数据库表，我们模拟保存成功
    // 在实际环境中，这里会插入到数据库
    /*
    for (const record of orderRecords) {
      await connection.execute(
        `INSERT INTO meal_orders (
          id, order_id, patient_id, patient_name, ward_name, bed_number,
          meal_id, meal_name, meal_category, meal_type, meal_date,
          quantity, unit_price, total_amount, order_date, order_time,
          status, description, image
        ) VALUES (
          :id, :orderId, :patientId, :patientName, :wardName, :bedNumber,
          :mealId, :mealName, :mealCategory, :mealType, :mealDate,
          :quantity, :unitPrice, :totalAmount, :orderDate, :orderTime,
          :status, :description, :image
        )`,
        record
      );
    }

    await connection.commit();
    */

    console.log('订单保存成功，订单ID:', orderId);

    res.json({
      success: true,
      message: '订单提交成功',
      orderId: orderId,
      orderCount: orderRecords.length,
      totalAmount: totalPrice
    });

  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      success: false,
      message: '订单提交失败: ' + error.message
    });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('创建订单 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 取消订单
router.post('/:orderId/cancel', async (req, res) => {
  const { orderId } = req.params;

  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `UPDATE orders SET status = '已取消' WHERE id = :orderId AND status = '待处理'`,
      { orderId }
    );

    if (result.rowsAffected === 0) {
      return res.status(400).json({ success: false, message: '订单无法取消' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

export default router;
