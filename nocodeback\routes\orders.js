import express from 'express';
import oracledb from 'oracledb';
import dayjs from 'dayjs';

const router = express.Router();

// 提交订单
router.post('/', async (req, res) => {
  const { patientId, wardId, items } = req.body;
  
  try {
    const connection = await oracledb.getConnection();
    
    // 开始事务
    await connection.execute(`BEGIN`);
    
    // 创建订单主表记录
    const orderResult = await connection.execute(
      `INSERT INTO orders (patient_id, ward_id, status, total_amount, order_date)
       VALUES (:patientId, :wardId, '待处理', 0, SYSDATE)
       RETURNING id INTO :orderId`,
      {
        patientId,
        wardId,
        orderId: { type: oracledb.STRING, dir: oracledb.BIND_OUT }
      }
    );
    
    const orderId = orderResult.outBinds.orderId[0];
    let totalAmount = 0;
    
    // 插入订单明细
    for (const item of items) {
      // 获取餐品价格
      const mealResult = await connection.execute(
        `SELECT price FROM meals WHERE id = :mealId`,
        { mealId: item.mealId }
      );
      
      if (mealResult.rows.length === 0) {
        await connection.execute(`ROLLBACK`);
        return res.status(400).json({ success: false, message: '餐品不存在' });
      }
      
      const price = mealResult.rows[0][0];
      totalAmount += price * item.quantity;
      
      await connection.execute(
        `INSERT INTO order_items (order_id, meal_id, quantity, price, date, meal_type)
         VALUES (:orderId, :mealId, :quantity, :price, TO_DATE(:date, 'YYYY-MM-DD'), :mealType)`,
        {
          orderId,
          mealId: item.mealId,
          quantity: item.quantity,
          price,
          date: item.date,
          mealType: item.mealType
        }
      );
    }
    
    // 更新订单总金额
    await connection.execute(
      `UPDATE orders SET total_amount = :totalAmount WHERE id = :orderId`,
      { totalAmount, orderId }
    );
    
    // 提交事务
    await connection.execute(`COMMIT`);
    
    res.json({ success: true, orderId });
  } catch (error) {
    console.error('提交订单错误:', error);
    await connection.execute(`ROLLBACK`);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取订单列表
router.get('/', async (req, res) => {
  const { status, patientId, wardId } = req.query;
  
  try {
    const connection = await oracledb.getConnection();
    let query = `
      SELECT o.id, o.patient_id, p.name as patient_name, b.number as bed_number,
             w.name as ward_name, o.order_date, o.status, o.total_amount,
             LISTAGG(oi.meal_type, ',') WITHIN GROUP (ORDER BY oi.id) as meal_types
      FROM orders o
      JOIN patients p ON o.patient_id = p.id
      JOIN beds b ON p.bed_id = b.id
      JOIN wards w ON b.ward_id = w.id
      JOIN order_items oi ON o.id = oi.order_id
      WHERE 1=1
    `;
    const binds = {};
    
    if (status && status !== '全部') {
      query += ` AND o.status = :status`;
      binds.status = status;
    }
    
    if (patientId) {
      query += ` AND o.patient_id = :patientId`;
      binds.patientId = patientId;
    }
    
    if (wardId) {
      query += ` AND b.ward_id = :wardId`;
      binds.wardId = wardId;
    }
    
    query += ` GROUP BY o.id, o.patient_id, p.name, b.number, w.name, o.order_date, o.status, o.total_amount`;
    
    const result = await connection.execute(query, binds);
    
    // 获取每个订单的明细
    const orders = await Promise.all(result.rows.map(async row => {
      const itemsResult = await connection.execute(
        `SELECT oi.meal_id, m.name, oi.price, oi.quantity, m.image
         FROM order_items oi
         JOIN meals m ON oi.meal_id = m.id
         WHERE oi.order_id = :orderId`,
        { orderId: row[0] }
      );
      
      const items = itemsResult.rows.map(item => ({
        mealId: item[0],
        name: item[1],
        price: item[2],
        quantity: item[3],
        image: item[4]
      }));
      
      return {
        id: row[0],
        patientId: row[1],
        patientName: row[2],
        bedNumber: row[3],
        wardName: row[4],
        date: dayjs(row[5]).format('YYYY-MM-DD'),
        mealType: row[7],
        items,
        status: row[6],
        total: row[8],
        deliveryTime: '12:00' // 默认送餐时间
      };
    }));
    
    res.json(orders);
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

// 取消订单
router.post('/:orderId/cancel', async (req, res) => {
  const { orderId } = req.params;
  
  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `UPDATE orders SET status = '已取消' WHERE id = :orderId AND status = '待处理'`,
      { orderId }
    );
    
    if (result.rowsAffected === 0) {
      return res.status(400).json({ success: false, message: '订单无法取消' });
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

export default router;
