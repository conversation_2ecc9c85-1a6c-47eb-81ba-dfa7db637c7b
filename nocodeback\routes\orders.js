import express from 'express';
import oracledb from 'oracledb';
import dayjs from 'dayjs';

const router = express.Router();

// 提交订单
router.post('/', async (req, res) => {
  const { patientId, wardId, items } = req.body;

  try {
    const connection = await oracledb.getConnection();

    // 开始事务
    await connection.execute(`BEGIN`);

    // 创建订单主表记录
    const orderResult = await connection.execute(
      `INSERT INTO orders (patient_id, ward_id, status, total_amount, order_date)
       VALUES (:patientId, :wardId, '待处理', 0, SYSDATE)
       RETURNING id INTO :orderId`,
      {
        patientId,
        wardId,
        orderId: { type: oracledb.STRING, dir: oracledb.BIND_OUT }
      }
    );

    const orderId = orderResult.outBinds.orderId[0];
    let totalAmount = 0;

    // 插入订单明细
    for (const item of items) {
      // 获取餐品价格
      const mealResult = await connection.execute(
        `SELECT price FROM meals WHERE id = :mealId`,
        { mealId: item.mealId }
      );

      if (mealResult.rows.length === 0) {
        await connection.execute(`ROLLBACK`);
        return res.status(400).json({ success: false, message: '餐品不存在' });
      }

      const price = mealResult.rows[0][0];
      totalAmount += price * item.quantity;

      await connection.execute(
        `INSERT INTO order_items (order_id, meal_id, quantity, price, date, meal_type)
         VALUES (:orderId, :mealId, :quantity, :price, TO_DATE(:date, 'YYYY-MM-DD'), :mealType)`,
        {
          orderId,
          mealId: item.mealId,
          quantity: item.quantity,
          price,
          date: item.date,
          mealType: item.mealType
        }
      );
    }

    // 更新订单总金额
    await connection.execute(
      `UPDATE orders SET total_amount = :totalAmount WHERE id = :orderId`,
      { totalAmount, orderId }
    );

    // 提交事务
    await connection.execute(`COMMIT`);

    res.json({ success: true, orderId });
  } catch (error) {
    console.error('提交订单错误:', error);
    await connection.execute(`ROLLBACK`);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 获取订单列表
router.get('/', async (req, res) => {
  const { status, patientId, wardId } = req.query;
  let connection;

  try {
    console.log('获取订单列表请求, status:', status, 'patientId:', patientId, 'wardId:', wardId);
    connection = await oracledb.getConnection();

    // 由于没有完整的数据库表，返回模拟数据
    const mockOrders = [
      {
        id: '1',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-07',
        mealType: '午餐',
        items: [
          {
            mealId: '201',
            name: '清蒸鱼',
            price: 28,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=fish&width=100&height=100'
          },
          {
            mealId: '101',
            name: '营养粥套餐',
            price: 15,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=porridge&width=100&height=100'
          }
        ],
        status: '已完成',
        total: 43,
        deliveryTime: '12:30'
      },
      {
        id: '2',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-06',
        mealType: '晚餐',
        items: [
          {
            mealId: '301',
            name: '米糊套餐',
            price: 18,
            quantity: 2,
            image: 'https://nocode.meituan.com/photo/search?keyword=rice,porridge&width=100&height=100'
          }
        ],
        status: '已取消',
        total: 36,
        deliveryTime: '18:15'
      },
      {
        id: '3',
        patientId: '2',
        patientName: '李四',
        bedNumber: '201-1',
        wardName: '外科一病区',
        date: '2024-12-05',
        mealType: '早餐',
        items: [
          {
            mealId: '102',
            name: '低盐低脂餐',
            price: 25,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=healthy,food&width=100&height=100'
          }
        ],
        status: '已完成',
        total: 25,
        deliveryTime: '08:45'
      },
      {
        id: '4',
        patientId: '1',
        patientName: '张三',
        bedNumber: '101-1',
        wardName: '内科一病区',
        date: '2024-12-04',
        mealType: '午餐',
        items: [
          {
            mealId: '202',
            name: '流质营养餐',
            price: 20,
            quantity: 1,
            image: 'https://nocode.meituan.com/photo/search?keyword=liquid,food&width=100&height=100'
          }
        ],
        status: '待处理',
        total: 20,
        deliveryTime: '12:00'
      }
    ];

    // 根据参数过滤数据
    let filteredOrders = mockOrders;

    if (status && status !== '全部') {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    if (patientId) {
      filteredOrders = filteredOrders.filter(order => order.patientId === patientId);
    }

    if (wardId) {
      // 根据病区过滤（简化处理）
      if (wardId === '1') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '张三');
      } else if (wardId === '2') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '李四');
      }
    }

    console.log('过滤后的订单数据:', filteredOrders);
    res.json(filteredOrders);

  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('订单列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 取消订单
router.post('/:orderId/cancel', async (req, res) => {
  const { orderId } = req.params;

  try {
    const connection = await oracledb.getConnection();
    const result = await connection.execute(
      `UPDATE orders SET status = '已取消' WHERE id = :orderId AND status = '待处理'`,
      { orderId }
    );

    if (result.rowsAffected === 0) {
      return res.status(400).json({ success: false, message: '订单无法取消' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

export default router;
