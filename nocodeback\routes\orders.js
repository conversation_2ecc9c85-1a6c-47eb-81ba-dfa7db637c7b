import express from 'express';
import oracledb from 'oracledb';

const router = express.Router();

// 内存存储订单数据（模拟数据库的备选方案）
let orderStorage = [];

// 获取订单列表
router.get('/', async (req, res) => {
  const { status, patientId, wardId } = req.query;
  let connection;

  try {
    console.log('获取订单列表请求, status:', status, 'patientId:', patientId, 'wardId:', wardId);
    connection = await oracledb.getConnection();

    // 从数据库查询订单数据
    let orders = [];
    
    try {
      // 先尝试从数据库查询
      let query = `
        SELECT 
          id, order_id, patient_id, patient_name, ward_name, bed_number,
          meal_id, meal_name, meal_category, meal_type, meal_date,
          quantity, unit_price, total_amount, order_date, order_time,
          status, description, image
        FROM meal_orders
        WHERE 1=1
      `;
      
      const binds = {};
      
      if (status && status !== '全部') {
        query += ` AND status = :status`;
        binds.status = status;
      }
      
      if (patientId) {
        query += ` AND patient_id = :patientId`;
        binds.patientId = patientId;
      }
      
      if (wardId) {
        query += ` AND ward_name LIKE :wardName`;
        binds.wardName = `%病区%`; // 简化处理，根据实际情况调整
      }
      
      query += ` ORDER BY order_time DESC`;
      
      console.log('执行查询:', query);
      console.log('查询参数:', binds);
      
      const result = await connection.execute(query, binds, {
        outFormat: oracledb.OUT_FORMAT_OBJECT
      });
      
      console.log('数据库查询结果:', result.rows.length, '条记录');
      
      // 将数据库结果转换为前端需要的格式
      const dbOrdersMap = new Map();
      
      result.rows.forEach(row => {
        const orderId = row.ORDER_ID;
        
        if (!dbOrdersMap.has(orderId)) {
          dbOrdersMap.set(orderId, {
            id: orderId,
            patientId: row.PATIENT_ID,
            patientName: row.PATIENT_NAME,
            bedNumber: row.BED_NUMBER,
            wardName: row.WARD_NAME,
            date: row.ORDER_DATE ? row.ORDER_DATE.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            mealType: row.MEAL_TYPE,
            items: [],
            status: row.STATUS,
            total: 0,
            deliveryTime: '12:00' // 默认送餐时间
          });
        }
        
        const order = dbOrdersMap.get(orderId);
        order.items.push({
          mealId: row.MEAL_ID,
          name: row.MEAL_NAME,
          price: row.UNIT_PRICE,
          quantity: row.QUANTITY,
          image: row.IMAGE || 'https://nocode.meituan.com/photo/search?keyword=food&width=100&height=100'
        });
        
        order.total += row.TOTAL_AMOUNT;
      });
      
      orders = Array.from(dbOrdersMap.values());
      console.log('数据库查询成功，返回', orders.length, '个订单');
      
    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      console.log('使用内存数据作为备选');
      
      // 如果数据库查询失败，使用内存数据和模拟数据
      const memoryOrdersMap = new Map();
      
      // 先处理内存中的订单
      orderStorage.forEach(record => {
        const orderId = record.orderId;
        
        if (!memoryOrdersMap.has(orderId)) {
          memoryOrdersMap.set(orderId, {
            id: orderId,
            patientId: record.patientId,
            patientName: record.patientName,
            bedNumber: record.bedNumber,
            wardName: record.wardName,
            date: record.orderDate,
            mealType: record.mealType,
            items: [],
            status: record.status,
            total: 0,
            deliveryTime: '12:00'
          });
        }
        
        const order = memoryOrdersMap.get(orderId);
        order.items.push({
          mealId: record.mealId,
          name: record.mealName,
          price: record.unitPrice,
          quantity: record.quantity,
          image: record.image
        });
        
        order.total += record.totalAmount;
      });
      
      orders = Array.from(memoryOrdersMap.values());
      
      // 如果内存中没有数据，使用模拟数据
      if (orders.length === 0) {
        console.log('使用模拟数据');
        orders = [
          {
            id: '1',
            patientId: '1',
            patientName: '张三',
            bedNumber: '101-1',
            wardName: '内科一病区',
            date: '2024-12-07',
            mealType: '午餐',
            items: [
              {
                mealId: '201',
                name: '清蒸鱼',
                price: 28,
                quantity: 1,
                image: 'https://nocode.meituan.com/photo/search?keyword=fish&width=100&height=100'
              },
              {
                mealId: '101',
                name: '营养粥套餐',
                price: 15,
                quantity: 1,
                image: 'https://nocode.meituan.com/photo/search?keyword=porridge&width=100&height=100'
              }
            ],
            status: '已完成',
            total: 43,
            deliveryTime: '12:30'
          },
          {
            id: '2',
            patientId: '1',
            patientName: '张三',
            bedNumber: '101-1',
            wardName: '内科一病区',
            date: '2024-12-06',
            mealType: '晚餐',
            items: [
              {
                mealId: '301',
                name: '米糊套餐',
                price: 18,
                quantity: 2,
                image: 'https://nocode.meituan.com/photo/search?keyword=rice,porridge&width=100&height=100'
              }
            ],
            status: '已取消',
            total: 36,
            deliveryTime: '18:15'
          },
          {
            id: '3',
            patientId: '2',
            patientName: '李四',
            bedNumber: '201-1',
            wardName: '外科一病区',
            date: '2024-12-05',
            mealType: '早餐',
            items: [
              {
                mealId: '102',
                name: '低盐低脂餐',
                price: 25,
                quantity: 1,
                image: 'https://nocode.meituan.com/photo/search?keyword=healthy,food&width=100&height=100'
              }
            ],
            status: '已完成',
            total: 25,
            deliveryTime: '08:45'
          },
          {
            id: '4',
            patientId: '1',
            patientName: '张三',
            bedNumber: '101-1',
            wardName: '内科一病区',
            date: '2024-12-04',
            mealType: '午餐',
            items: [
              {
                mealId: '202',
                name: '流质营养餐',
                price: 20,
                quantity: 1,
                image: 'https://nocode.meituan.com/photo/search?keyword=liquid,food&width=100&height=100'
              }
            ],
            status: '待处理',
            total: 20,
            deliveryTime: '12:00'
          }
        ];
      }
    }
    
    // 根据参数过滤数据
    let filteredOrders = orders;
    
    if (status && status !== '全部') {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }
    
    if (patientId) {
      filteredOrders = filteredOrders.filter(order => order.patientId === patientId);
    }
    
    if (wardId) {
      // 根据病区过滤（简化处理）
      if (wardId === '1') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '张三');
      } else if (wardId === '2') {
        filteredOrders = filteredOrders.filter(order => order.patientName === '李四');
      }
    }
    
    console.log('过滤后的订单数据:', filteredOrders.length, '个订单');
    res.json(filteredOrders);
    
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({ message: '服务器错误: ' + error.message });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('订单列表查询 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 创建新订单
router.post('/', async (req, res) => {
  const { patientId, patientName, wardName, bedNumber, items, totalPrice } = req.body;
  let connection;
  
  try {
    console.log('创建订单请求:', req.body);
    connection = await oracledb.getConnection();
    
    // 生成订单ID
    const orderId = Date.now().toString();
    const orderDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 为每个餐品创建订单记录
    const orderRecords = [];
    
    for (const item of items) {
      const orderRecord = {
        id: `${orderId}_${item.id}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        orderId: orderId,
        patientId: patientId,
        patientName: patientName,
        wardName: wardName,
        bedNumber: bedNumber,
        mealId: item.id,
        mealName: item.name,
        mealCategory: item.category,
        mealType: item.mealTime, // 餐次（早餐、午餐、晚餐）
        mealDate: item.mealDate.split('T')[0], // 用餐日期
        quantity: item.quantity,
        unitPrice: item.price,
        totalAmount: item.price * item.quantity,
        orderDate: orderDate,
        orderTime: new Date().toISOString(),
        status: '待处理',
        description: item.description || '',
        image: item.image || ''
      };
      
      orderRecords.push(orderRecord);
    }
    
    console.log('订单记录:', orderRecords);
    
    // 将订单记录保存到数据库
    try {
      for (const record of orderRecords) {
        await connection.execute(
          `INSERT INTO meal_orders (
            id, order_id, patient_id, patient_name, ward_name, bed_number,
            meal_id, meal_name, meal_category, meal_type, meal_date,
            quantity, unit_price, total_amount, order_date, order_time,
            status, description, image
          ) VALUES (
            :id, :orderId, :patientId, :patientName, :wardName, :bedNumber,
            :mealId, :mealName, :mealCategory, :mealType, TO_DATE(:mealDate, 'YYYY-MM-DD'),
            :quantity, :unitPrice, :totalAmount, TO_DATE(:orderDate, 'YYYY-MM-DD'), 
            TO_TIMESTAMP(:orderTime, 'YYYY-MM-DD"T"HH24:MI:SS.FF3"Z"'),
            :status, :description, :image
          )`,
          record
        );
      }
      
      await connection.commit();
      console.log('订单保存成功（数据库），订单ID:', orderId);
      
    } catch (dbError) {
      console.error('数据库保存失败:', dbError);
      await connection.rollback();
      
      // 如果数据库保存失败，使用内存存储作为备选
      console.log('使用内存存储作为备选');
      orderStorage.push(...orderRecords);
      console.log('订单保存成功（内存），订单ID:', orderId);
    }
    
    res.json({
      success: true,
      message: '订单提交成功',
      orderId: orderId,
      orderCount: orderRecords.length,
      totalAmount: totalPrice
    });
    
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '订单提交失败: ' + error.message 
    });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('创建订单 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

// 取消订单
router.post('/:orderId/cancel', async (req, res) => {
  const { orderId } = req.params;
  let connection;
  
  try {
    connection = await oracledb.getConnection();
    
    // 尝试更新数据库中的订单状态
    try {
      const result = await connection.execute(
        `UPDATE meal_orders SET status = '已取消' WHERE order_id = :orderId AND status = '待处理'`,
        { orderId }
      );
      
      await connection.commit();
      
      if (result.rowsAffected === 0) {
        return res.status(400).json({ success: false, message: '订单无法取消或不存在' });
      }
      
      console.log('订单取消成功（数据库）:', orderId);
      
    } catch (dbError) {
      console.error('数据库更新失败:', dbError);
      await connection.rollback();
      
      // 如果数据库更新失败，尝试更新内存中的数据
      const updated = orderStorage.filter(record => {
        if (record.orderId === orderId && record.status === '待处理') {
          record.status = '已取消';
          return true;
        }
        return false;
      });
      
      if (updated.length === 0) {
        return res.status(400).json({ success: false, message: '订单无法取消或不存在' });
      }
      
      console.log('订单取消成功（内存）:', orderId);
    }
    
    res.json({ success: true, message: '订单已取消' });
    
  } catch (error) {
    console.error('取消订单错误:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('取消订单 - 数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
});

export default router;
