import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Home, ShoppingCart, User } from 'lucide-react';

const BottomNav = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* 主按钮 */}
        <button 
          onClick={() => setIsExpanded(!isExpanded)}
          className="bg-blue-500 text-white rounded-full p-4 shadow-lg hover:bg-blue-600 transition-all duration-300 hover:shadow-xl"
        >
          <div className={`transition-transform duration-300 ${isExpanded ? 'rotate-45' : ''}`}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
          </div>
        </button>
        
        {/* 展开的菜单项 */}
        <div className={`absolute bottom-full right-0 mb-2 flex flex-col items-end space-y-2 transition-all duration-300 ${isExpanded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2 pointer-events-none'}`}>
          <Link
            to="/"
            className="bg-blue-500 rounded-full shadow-md p-3 flex items-center justify-center text-white hover:bg-blue-600 transition-all transform hover:scale-110"
            title="首页"
          >
            <Home className="h-5 w-5" />
          </Link>
          <Link
            to="/orders"
            className="bg-blue-500 rounded-full shadow-md p-3 flex items-center justify-center text-white hover:bg-blue-600 transition-all transform hover:scale-110"
            title="订单"
          >
            <ShoppingCart className="h-5 w-5" />
          </Link>
          <Link
            to="/profile"
            className="bg-blue-500 rounded-full shadow-md p-3 flex items-center justify-center text-white hover:bg-blue-600 transition-all transform hover:scale-110"
            title="我的"
          >
            <User className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BottomNav;
