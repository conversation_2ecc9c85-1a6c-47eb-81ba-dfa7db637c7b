-- 创建用户表
CREATE TABLE users (
  id VARCHAR2(36) PRIMARY KEY,
  username VARCHAR2(50) NOT NULL UNIQUE,
  password VARCHAR2(100) NOT NULL,
  role VARCHAR2(20) NOT NULL,
  created_at TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- 创建医生表
CREATE TABLE doctors (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  specialty VARCHAR2(100),
  phone VARCHAR2(20)
);

-- 创建病区表
CREATE TABLE wards (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  doctor_id VARCHAR2(36) REFERENCES doctors(id)
);

-- 创建护士表
CREATE TABLE nurses (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  ward_id VARCHAR2(36) REFERENCES wards(id),
  phone VARCHAR2(20)
);

-- 创建病床表
CREATE TABLE beds (
  id VARCHAR2(36) PRIMARY KEY,
  number VARCHAR2(20) NOT NULL,
  ward_id VARCHAR2(36) REFERENCES wards(id)
);

-- 创建病人表
CREATE TABLE patients (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  bed_id VARCHAR2(36) REFERENCES beds(id),
  age NUMBER,
  gender VARCHAR2(10),
  diagnosis VARCHAR2(200),
  admission_date DATE,
  phone VARCHAR2(20),
  email VARCHAR2(100),
  diet_restrictions VARCHAR2(200),
  avatar VARCHAR2(200)
);

-- 创建餐品分类表
CREATE TABLE meal_categories (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  description VARCHAR2(200)
);

-- 创建餐品表
CREATE TABLE meals (
  id VARCHAR2(36) PRIMARY KEY,
  name VARCHAR2(50) NOT NULL,
  description VARCHAR2(200),
  price NUMBER(10,2) NOT NULL,
  image VARCHAR2(200),
  category VARCHAR2(50) NOT NULL,
  meal_type VARCHAR2(20),
  suitable_for VARCHAR2(200)
);

-- 创建订单表
CREATE TABLE orders (
  id VARCHAR2(36) PRIMARY KEY,
  patient_id VARCHAR2(36) REFERENCES patients(id),
  ward_id VARCHAR2(36) REFERENCES wards(id),
  status VARCHAR2(20) DEFAULT '待处理',
  total_amount NUMBER(10,2) DEFAULT 0,
  order_date TIMESTAMP DEFAULT SYSTIMESTAMP
);

-- 创建订单明细表
CREATE TABLE order_items (
  id VARCHAR2(36) PRIMARY KEY,
  order_id VARCHAR2(36) REFERENCES orders(id),
  meal_id VARCHAR2(36) REFERENCES meals(id),
  quantity NUMBER DEFAULT 1,
  price NUMBER(10,2) NOT NULL,
  date DATE NOT NULL,
  meal_type VARCHAR2(20) NOT NULL
);

-- 创建测试数据
INSERT INTO users VALUES ('1', 'admin', '$2a$10$xJwL5v5zLt2J5Z5X5X5X5e', 'admin', SYSTIMESTAMP);

INSERT INTO doctors VALUES ('1', '张医生', '内科', '13800138000');
INSERT INTO doctors VALUES ('2', '李医生', '外科', '13800138001');

INSERT INTO wards VALUES ('1', '内科一病区', '1');
INSERT INTO wards VALUES ('2', '外科一病区', '2');

INSERT INTO nurses VALUES ('1', '王护士', '1', '13800138002');
INSERT INTO nurses VALUES ('2', '赵护士', '2', '13800138003');

INSERT INTO beds VALUES ('1', '101-1', '1');
INSERT INTO beds VALUES ('2', '101-2', '1');
INSERT INTO beds VALUES ('3', '201-1', '2');
INSERT INTO beds VALUES ('4', '201-2', '2');

INSERT INTO patients VALUES (
  '1', '张三', '1', 45, '男', '高血压', 
  TO_DATE('2023-01-15', 'YYYY-MM-DD'), '13800138004', '<EMAIL>', 
  '低盐,低脂', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
);
INSERT INTO patients VALUES (
  '2', '李四', '3', 32, '女', '骨折', 
  TO_DATE('2023-02-20', 'YYYY-MM-DD'), '13800138005', '<EMAIL>', 
  '高钙', 'https://nocode.meituan.com/photo/search?keyword=patient&width=200&height=200'
);

INSERT INTO meals VALUES (
  '1', '清蒸鱼', '新鲜草鱼清蒸', 25.00, 
  'https://nocode.meituan.com/photo/search?keyword=fish&width=400&height=300', 
  '普通餐', '午餐', '高血压,糖尿病'
);
INSERT INTO meals VALUES (
  '2', '排骨汤', '猪骨熬制', 18.00, 
  'https://nocode.meituan.com/photo/search?keyword=soup&width=400&height=300', 
  '普通餐', '晚餐', '骨折,术后'
);
INSERT INTO meals VALUES (
  '3', '小米粥', '养胃小米粥', 8.00, 
  'https://nocode.meituan.com/photo/search?keyword=porridge&width=400&height=300', 
  '流质', '早餐', '术后,胃病'
);
