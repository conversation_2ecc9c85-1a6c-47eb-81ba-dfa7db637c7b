import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import oracledb from 'oracledb';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import dayjs from 'dayjs';

const app = express();
const PORT = process.env.PORT || 5000;

// 中间件
app.use(cors());
app.use(express.json());

// Oracle数据库连接池配置 E:\rongyao\instantclient_19_19
oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});
oracledb.autoCommit = true;

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING,
  poolMin: 2,
  poolMax: 10,
  poolIncrement: 1
};

// 创建数据库连接池
let pool;
async function initialize() {
  try {
    pool = await oracledb.createPool(dbConfig);
    console.log('数据库连接池初始化成功');
  } catch (err) {
    console.error('数据库连接池初始化失败:', err);
  }
}
initialize();

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) return res.sendStatus(401);
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};

// 路由
import authRouter from './routes/auth.js';
import wardsRouter from './routes/wards.js';
import patientsRouter from './routes/patients.js';
import mealsRouter from './routes/meals.js';
import ordersRouter from './routes/orders.js';
import statsRouter from './routes/stats.js';
import dbTestRouter from './routes/dbTest.js';

// 注册路由
app.use('/api/auth', authRouter);
app.use('/api/db', dbTestRouter); // 将数据库测试路由放在前面
app.use('/api/wards', authenticateToken, wardsRouter);
app.use('/api/patients', authenticateToken, patientsRouter);
app.use('/api/meal', authenticateToken, mealsRouter);
app.use('/api/orders', authenticateToken, ordersRouter);
app.use('/api/stats', authenticateToken, statsRouter);

// 添加一个测试路由
app.get('/test', (req, res) => {
  res.json({ message: '服务器正常运行' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    message: '服务器错误',
    error: err.message
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
});

// 导出app用于测试
export default app;
