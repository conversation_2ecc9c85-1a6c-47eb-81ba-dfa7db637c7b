import express from 'express';
import oracledb from 'oracledb';
import dayjs from 'dayjs';

const router = express.Router();

// 获取订单统计数据
router.get('/orders', async (req, res) => {
  const { wardId, patientId, timeRange } = req.query;
  
  try {
    const connection = await oracledb.getConnection();
    
    // 基础统计
    let query = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = '已完成' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = '已取消' THEN 1 ELSE 0 END) as canceled
      FROM orders o
      JOIN patients p ON o.patient_id = p.id
      JOIN beds b ON p.bed_id = b.id
      WHERE 1=1
    `;
    const binds = {};
    
    if (wardId) {
      query += ` AND b.ward_id = :wardId`;
      binds.wardId = wardId;
    }
    
    if (patientId) {
      query += ` AND o.patient_id = :patientId`;
      binds.patientId = patientId;
    }
    
    const statsResult = await connection.execute(query, binds);
    const stats = {
      total: statsResult.rows[0][0],
      completed: statsResult.rows[0][1],
      canceled: statsResult.rows[0][2]
    };
    
    // 月度数据
    let monthlyQuery = `
      SELECT 
        TO_CHAR(order_date, 'YYYY-MM') as month,
        COUNT(*) as orders
      FROM orders o
      JOIN patients p ON o.patient_id = p.id
      JOIN beds b ON p.bed_id = b.id
      WHERE 1=1
    `;
    
    if (wardId) {
      monthlyQuery += ` AND b.ward_id = :wardId`;
    }
    
    if (patientId) {
      monthlyQuery += ` AND o.patient_id = :patientId`;
    }
    
    monthlyQuery += `
      GROUP BY TO_CHAR(order_date, 'YYYY-MM')
      ORDER BY month
    `;
    
    const monthlyResult = await connection.execute(monthlyQuery, binds);
    stats.monthlyData = monthlyResult.rows.map(row => ({
      month: row[0],
      orders: row[1]
    }));
    
    res.json(stats);
  } catch (error) {
    console.error('获取统计数据错误:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});

export default router;
