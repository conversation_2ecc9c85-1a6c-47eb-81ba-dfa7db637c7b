import oracledb from 'oracledb';
import 'dotenv/config';

// 初始化Oracle客户端
oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function createBeds() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('数据库连接成功');
    
    // 创建 BEDS 表
    console.log('创建 BEDS 表...');
    await connection.execute(`
      CREATE TABLE beds (
        id VARCHAR2(36) PRIMARY KEY,
        number VARCHAR2(20) NOT NULL,
        ward_id VARCHAR2(36)
      )
    `);
    console.log('BEDS 表创建成功');
    
    // 插入床位数据
    console.log('插入床位数据...');
    await connection.execute(`INSERT INTO beds (id, number, ward_id) VALUES ('1', '101-1', '1')`);
    await connection.execute(`INSERT INTO beds (id, number, ward_id) VALUES ('2', '101-2', '1')`);
    await connection.execute(`INSERT INTO beds (id, number, ward_id) VALUES ('3', '201-1', '2')`);
    await connection.execute(`INSERT INTO beds (id, number, ward_id) VALUES ('4', '201-2', '2')`);
    console.log('床位数据插入成功');
    
    // 提交事务
    await connection.commit();
    
    // 验证数据
    const bedsResult = await connection.execute(
      `SELECT * FROM beds`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    console.log('床位数据:', bedsResult.rows);
    
    // 测试病区查询
    console.log('测试病区查询...');
    const wardsResult = await connection.execute(
      `SELECT w.id, w.name, COUNT(b.id) as bed_count, d.name as doctor 
       FROM wards w
       LEFT JOIN beds b ON w.id = b.ward_id
       LEFT JOIN doctors d ON w.doctor_id = d.id
       GROUP BY w.id, w.name, d.name`,
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('病区查询结果:', wardsResult.rows);
    
  } catch (error) {
    console.error('创建床位表失败:', error);
  } finally {
    if (connection) {
      try {
        await connection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭连接时出错:', err);
      }
    }
  }
}

createBeds();
