import { useState } from 'react';
// 添加 useSearchParams 导入
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { ShoppingCart, Search, Star, Clock, MapPin, ChevronRight, Plus, Minus, Calendar, User, LogOut, ArrowLeft } from 'lucide-react';

const Index = () => {
  // 添加 searchParams 获取 URL 参数
  const [searchParams] = useSearchParams();
  const patientId = searchParams.get('patientId');
  
  // 添加病人信息的状态
  const [patientInfo, setPatientInfo] = useState(() => {
    // 模拟从 Patients 页面获取的病人数据
    const mockPatients = [
      { id: '101', name: '张伟', bedNumber: '301-3', wardName: '内科一病区' },
      { id: '102', name: '李娜', bedNumber: '302-1', wardName: '内科一病区' },
      { id: '103', name: '王强', bedNumber: '303-2', wardName: '内科一病区' },
      { id: '104', name: '赵敏', bedNumber: '304-4', wardName: '内科一病区' },
    ];
    return mockPatients.find(p => p.id === patientId) || null;
  });

  const [cartItems, setCartItems] = useState([]);
  const [activeCategory, setActiveCategory] = useState('全部');
  const [showCart, setShowCart] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedMeal, setSelectedMeal] = useState('早餐');
  const navigate = useNavigate();

  const handleLogout = () => {
    navigate('/login');
  };

  // 添加返回函数
  const handleBackToPatients = () => {
    navigate('/patients/1');
  };

  const categories = ['全部', '普通餐', '治疗餐', '小炒', '流质', '半流质'];
  const meals = ['早餐', '午餐', '晚餐'];
  
  const restaurants = [
    {
      id: 1,
      name: '医院营养早餐',
      category: '普通餐',
      mealTime: '早餐',
      dishes: [
        { 
          id: 101, 
          name: '营养粥套餐', 
          price: 15,
          mealTime: '早餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=porridge&width=200&height=200' 
        },
        { 
          id: 102, 
          name: '低盐低脂餐', 
          price: 25,
          mealTime: '早餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=healthy,food&width=200&height=200' 
        }
      ]
    },
    {
      id: 2,
      name: '医院营养午餐',
      category: '治疗餐',
      mealTime: '午餐',
      dishes: [
        { 
          id: 201, 
          name: '流质营养餐', 
          price: 20,
          mealTime: '午餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=liquid,food&width=200&height=200' 
        },
        { 
          id: 202, 
          name: '糖尿病专用餐', 
          price: 30,
          mealTime: '午餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=diabetic,food&width=200&height=200' 
        }
      ]
    },
    {
      id: 3,
      name: '医院营养晚餐',
      category: '半流质',
      mealTime: '晚餐',
      dishes: [
        { 
          id: 301, 
          name: '米糊套餐', 
          price: 18,
          mealTime: '晚餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=rice,porridge&width=200&height=200' 
        },
        { 
          id: 302, 
          name: '蛋花汤套餐', 
          price: 22,
          mealTime: '晚餐',
          image: 'https://nocode.meituan.com/photo/search?keyword=egg,soup&width=200&height=200' 
        }
      ]
    }
  ];

  // 修改 addToCart 函数
  const addToCart = (dish) => {
    setCartItems(prevItems => {
      // 使用当前选择的日期和餐次
      const mealDate = selectedDate;
      
      const existingItem = prevItems.find(item => 
        item.id === dish.id && 
        item.mealTime === selectedMeal &&
        item.mealDate.toDateString() === mealDate.toDateString()
      );
      
      if (existingItem) {
        return prevItems.map(item =>
          item.id === dish.id && 
          item.mealTime === selectedMeal &&
          item.mealDate.toDateString() === mealDate.toDateString()
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevItems, { 
          ...dish, 
          quantity: 1,
          mealDate: mealDate,
          mealTime: selectedMeal
        }];
      }
    });
  };

  const removeFromCart = (dishId) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === dishId);
      if (existingItem && existingItem.quantity > 1) {
        return prevItems.map(item =>
          item.id === dishId 
            ? { ...item, quantity: item.quantity - 1 } 
            : item
        );
      } else {
        return prevItems.filter(item => item.id !== dishId);
      }
    });
  };

  const toggleCart = () => {
    setShowCart(!showCart);
  };

  // 修改 handleConfirmOrder 函数
  const handleConfirmOrder = () => {
    const orderData = {
      patient: {
        id: patientId,
        name: patientInfo?.name,
        wardName: patientInfo?.wardName,
        bedNumber: patientInfo?.bedNumber,
      },
      orderTime: new Date().toISOString(), // 下单时间
      items: cartItems.map(item => ({
        ...item,
        mealDate: item.mealDate.toISOString(), // 用餐日期
        mealTime: item.mealTime, // 餐次
      })),
      totalPrice: totalPrice
    };

    console.log('订单数据:', orderData);
    alert('订单已提交，请查看控制台中的订单数据');
    setCartItems([]);
    setShowCart(false);
  };

  // 替换原有的筛选逻辑
  const filteredRestaurants = restaurants
    .filter(restaurant => {
      const categoryMatch = activeCategory === '全部' || restaurant.category === activeCategory;
      const mealTimeMatch = restaurant.mealTime === selectedMeal;
      return categoryMatch && mealTimeMatch;
    });

  const totalPrice = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  const formatDate = (date) => {
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      weekday: 'short'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部搜索栏 */}
      <div className="bg-blue-500 p-4 sticky top-0 z-10">
        <div className="flex flex-col">
          {/* 添加病人信息显示 */}
          {patientInfo && (
            <div className="text-white text-sm mb-2">
              <span>{patientInfo.wardName}</span>
              <span className="mx-2">|</span>
              <span>{patientInfo.bedNumber}</span>
              <span className="mx-2">|</span>
              <span>{patientInfo.name}</span>
            </div>
          )}
          {/* 现有的顶部内容 */}
          <div className="flex items-center justify-between">
            <h1 className="text-white text-xl font-bold">营养点餐</h1>
            <div className="relative w-1/2">
              <input 
                type="text" 
                placeholder="搜索餐品..." 
                className="w-full py-2 px-4 pr-10 rounded-full text-sm focus:outline-none"
              />
              <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* 日期和餐次选择 */}
      <div className="bg-white p-4 border-b">
        <div className="flex items-center mb-3">
          <Calendar className="h-5 w-5 text-blue-500 mr-2" />
          <input
            type="date"
            value={selectedDate.toISOString().split('T')[0]}
            onChange={(e) => setSelectedDate(new Date(e.target.value))}
            className="border rounded px-3 py-1 text-sm"
            min={new Date().toISOString().split('T')[0]}
          />
          <span className="ml-3 text-sm font-medium">{formatDate(selectedDate)}</span>
        </div>
        <div className="flex space-x-2">
          {meals.map(meal => (
            <button
              key={meal}
              onClick={() => setSelectedMeal(meal)}
              className={`px-4 py-2 rounded-full text-sm ${
                selectedMeal === meal 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {meal}
            </button>
          ))}
        </div>
      </div>

      {/* 购物车弹窗 */}
      {showCart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-20 flex justify-end">
          <div className="bg-white w-full max-w-md h-full overflow-y-auto">
            <div className="p-4 border-b sticky top-0 bg-white z-10">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">我的点餐</h2>
                <button onClick={toggleCart} className="text-gray-500">
                  ×
                </button>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                {formatDate(selectedDate)} {selectedMeal}
              </div>
            </div>
            
            {cartItems.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                购物车是空的
              </div>
            ) : (
              <div className="divide-y">
                {cartItems.map(item => (
                  <div key={item.id} className="p-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <img 
                        src={item.image} 
                        alt={item.name}
                        className="w-12 h-12 rounded object-cover mr-3"
                      />
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(item.mealDate)} {item.mealTime}
                        </p>
                        <p className="text-blue-500">¥{item.price}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <button 
                        onClick={() => removeFromCart(item.id)}
                        className="bg-gray-200 text-gray-700 rounded-full p-1"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                      <span className="mx-2 w-8 text-center">{item.quantity}</span>
                      <button 
                        onClick={() => addToCart(item)}
                        className="bg-blue-500 text-white rounded-full p-1"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {cartItems.length > 0 && (
              <div className="p-4 border-t sticky bottom-0 bg-white">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-bold">总计:</span>
                  <span className="text-blue-500 font-bold text-lg">¥{totalPrice}</span>
                </div>
                <button 
                  className="w-full bg-blue-500 text-white py-3 rounded-lg font-bold"
                  onClick={handleConfirmOrder}
                >
                  确认点餐
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 分类导航 */}
      <div className="bg-white p-4 overflow-x-auto">
        <div className="flex space-x-4">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-full whitespace-nowrap ${
                activeCategory === category 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* 膳食列表 */}
      <div className="p-4 space-y-4">
        {filteredRestaurants.map(restaurant => (
          <div key={restaurant.id} className="bg-white rounded-lg shadow p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-bold text-lg">{restaurant.name}</h3>
            </div>

            <div className="space-y-3">
              {restaurant.dishes.map(dish => (
                <div key={dish.id} className="flex items-center justify-between p-2 border rounded-lg">
                  <div className="flex items-center">
                    <img 
                      src={dish.image} 
                      alt={dish.name}
                      className="w-12 h-12 rounded object-cover mr-3"
                    />
                    <div>
                      <p className="font-medium">{dish.name}</p>
                      <p className="text-blue-500 text-sm">¥{dish.price}</p>
                    </div>
                  </div>
                  <button 
                    onClick={() => addToCart(dish)}
                    className="bg-blue-500 text-white rounded-full p-1.5"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 购物车浮动按钮 - 修改为左下角 */}
      {cartItems.length > 0 && !showCart && (
        <div 
          className="fixed bottom-20 left-6 bg-blue-500 text-white rounded-full p-4 shadow-lg flex items-center cursor-pointer"
          onClick={toggleCart}
        >
          <ShoppingCart className="h-6 w-6 mr-2" />
          <span className="font-bold">¥{totalPrice}</span>
        </div>
      )}

      {/* 在返回语句的最后，购物车浮动按钮后面添加底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-20">
        <div className="flex justify-around items-center h-16">
          <button 
            onClick={handleBackToPatients}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <ArrowLeft className="h-6 w-6" />
            <span className="text-xs mt-1">病人列表</span>
          </button>

          <button 
            onClick={toggleCart}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500 relative"
          >
            <ShoppingCart className="h-6 w-6" />
            <span className="text-xs mt-1">购物车</span>
            {cartItems.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
              </span>
            )}
          </button>

          <Link 
            to="/orders" 
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <User className="h-6 w-6" />
            <span className="text-xs mt-1">订单记录</span>
          </Link>

          <button 
            onClick={handleLogout}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <LogOut className="h-6 w-6" />
            <span className="text-xs mt-1">退出系统</span>
          </button>
        </div>
      </div>

      {/* 为底部导航腾出空间 */}
      <div className="h-16"></div>
    </div>
  );
};

export default Index;
