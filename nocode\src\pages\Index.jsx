import { useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { ShoppingCart, Search, Plus, Minus, Calendar, User, LogOut, ArrowLeft } from 'lucide-react';
import { useQuery, useMutation } from '@tanstack/react-query';

const Index = () => {
  // 添加 searchParams 获取 URL 参数
  const [searchParams] = useSearchParams();
  const patientId = searchParams.get('patientId');

  // 使用 API 获取病人信息
  const { data: patientInfo, isLoading: isLoadingPatient, error: patientError } = useQuery({
    queryKey: ['patient', patientId],
    queryFn: async () => {
      if (!patientId) return null;

      console.log('获取病人详情, patientId:', patientId);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const response = await fetch(`/api/patients/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('病人详情响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        if (response.status === 404) {
          throw new Error('病人不存在');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '获取病人详情失败');
      }

      const data = await response.json();
      console.log('病人详情数据:', data);
      return data;
    },
    enabled: !!patientId,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  const [cartItems, setCartItems] = useState([]);
  const [activeCategory, setActiveCategory] = useState('全部');
  const [showCart, setShowCart] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedMeal, setSelectedMeal] = useState('早餐');
  const navigate = useNavigate();

  const handleLogout = () => {
    navigate('/login');
  };

  // 添加返回函数
  const handleBackToPatients = () => {
    navigate('/patients/1');
  };

  // 使用 API 获取餐品分类
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['mealCategories'],
    queryFn: async () => {
      console.log('获取餐品分类');
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const response = await fetch('/api/meals/categories', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        throw new Error('获取餐品分类失败');
      }

      const data = await response.json();
      console.log('餐品分类数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 10 * 60 * 1000, // 10分钟缓存
  });

  const meals = ['早餐', '午餐', '晚餐'];

  // 订单提交 mutation
  const orderMutation = useMutation({
    mutationFn: async (orderData) => {
      console.log('提交订单数据:', orderData);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      console.log('订单提交响应:', response.status, response.statusText);

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '订单提交失败');
      }

      const data = await response.json();
      console.log('订单提交成功:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('订单提交成功回调:', data);
      alert(`订单提交成功！\n订单号: ${data.orderId}\n订单数量: ${data.orderCount} 条\n总金额: ¥${data.totalAmount}`);
      setCartItems([]);
      setShowCart(false);
    },
    onError: (error) => {
      console.error('订单提交失败:', error);
      alert('订单提交失败: ' + error.message);
    }
  });

  // 使用 API 获取餐品列表
  const { data: meals_data = [], isLoading: isLoadingMeals } = useQuery({
    queryKey: ['meals', activeCategory, selectedMeal],
    queryFn: async () => {
      console.log('获取餐品列表, category:', activeCategory, 'mealType:', selectedMeal);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const params = new URLSearchParams();
      if (activeCategory && activeCategory !== '全部') {
        params.append('category', activeCategory);
      }
      if (selectedMeal) {
        params.append('mealType', selectedMeal);
      }

      const response = await fetch(`/api/meals?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
          throw new Error('登录已过期，请重新登录');
        }
        throw new Error('获取餐品列表失败');
      }

      const data = await response.json();
      console.log('餐品列表数据:', data);
      return data;
    },
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 修改 addToCart 函数
  const addToCart = (dish) => {
    setCartItems(prevItems => {
      // 使用当前选择的日期和餐次
      const mealDate = selectedDate;

      const existingItem = prevItems.find(item =>
        item.id === dish.id &&
        item.mealTime === selectedMeal &&
        item.mealDate.toDateString() === mealDate.toDateString()
      );

      if (existingItem) {
        return prevItems.map(item =>
          item.id === dish.id &&
          item.mealTime === selectedMeal &&
          item.mealDate.toDateString() === mealDate.toDateString()
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevItems, {
          ...dish,
          quantity: 1,
          mealDate: mealDate,
          mealTime: selectedMeal
        }];
      }
    });
  };

  const removeFromCart = (dishId) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === dishId);
      if (existingItem && existingItem.quantity > 1) {
        return prevItems.map(item =>
          item.id === dishId
            ? { ...item, quantity: item.quantity - 1 }
            : item
        );
      } else {
        return prevItems.filter(item => item.id !== dishId);
      }
    });
  };

  const toggleCart = () => {
    setShowCart(!showCart);
  };

  // 修改 handleConfirmOrder 函数
  const handleConfirmOrder = () => {
    if (!patientInfo) {
      alert('病人信息不存在，无法提交订单');
      return;
    }

    if (cartItems.length === 0) {
      alert('购物车为空，请先添加餐品');
      return;
    }

    // 构建订单数据，包含完整的病人和餐品信息
    const orderData = {
      patientId: patientId,
      patientName: patientInfo.name,
      wardName: patientInfo.wardName,
      bedNumber: patientInfo.bedNumber,
      items: cartItems.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        price: item.price,
        image: item.image,
        category: item.category,
        quantity: item.quantity,
        mealDate: item.mealDate.toISOString(), // 用餐日期
        mealTime: item.mealTime, // 餐次（早餐、午餐、晚餐）
      })),
      totalPrice: totalPrice
    };

    console.log('准备提交订单:', orderData);

    // 使用 mutation 提交订单
    orderMutation.mutate(orderData);
  };

  // 餐品数据已经通过 API 过滤，直接使用

  const totalPrice = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  const formatDate = (date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      weekday: 'short'
    });
  };

  // 如果没有 patientId，显示错误信息
  if (!patientId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">缺少病人信息</h2>
          <p className="text-gray-600 mb-4">请从病人列表页面进入</p>
          <button
            onClick={() => navigate('/wards')}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            返回病区列表
          </button>
        </div>
      </div>
    );
  }

  // 加载状态
  if (isLoadingPatient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载病人信息中...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (patientError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{patientError.message}</p>
          <div className="space-x-4">
            <button
              onClick={() => navigate('/wards')}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              返回病区列表
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部搜索栏 */}
      <div className="bg-blue-500 p-4 sticky top-0 z-10">
        <div className="flex flex-col">
          {/* 添加病人信息显示 */}
          {patientInfo && (
            <div className="text-white text-sm mb-2">
              <span>{patientInfo.wardName}</span>
              <span className="mx-2">|</span>
              <span>{patientInfo.bedNumber}</span>
              <span className="mx-2">|</span>
              <span>{patientInfo.name}</span>
            </div>
          )}
          {/* 现有的顶部内容 */}
          <div className="flex items-center justify-between">
            <h1 className="text-white text-xl font-bold">营养点餐</h1>
            <div className="relative w-1/2">
              <input
                type="text"
                placeholder="搜索餐品..."
                className="w-full py-2 px-4 pr-10 rounded-full text-sm focus:outline-none"
              />
              <Search className="absolute right-3 top-2.5 h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* 日期和餐次选择 */}
      <div className="bg-white p-4 border-b">
        <div className="flex items-center mb-3">
          <Calendar className="h-5 w-5 text-blue-500 mr-2" />
          <input
            type="date"
            value={selectedDate.toISOString().split('T')[0]}
            onChange={(e) => setSelectedDate(new Date(e.target.value))}
            className="border rounded px-3 py-1 text-sm"
            min={new Date().toISOString().split('T')[0]}
          />
          <span className="ml-3 text-sm font-medium">{formatDate(selectedDate)}</span>
        </div>
        <div className="flex space-x-2">
          {meals.map(meal => (
            <button
              key={meal}
              onClick={() => setSelectedMeal(meal)}
              className={`px-4 py-2 rounded-full text-sm ${
                selectedMeal === meal
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {meal}
            </button>
          ))}
        </div>
      </div>

      {/* 购物车弹窗 */}
      {showCart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-20 flex justify-end">
          <div className="bg-white w-full max-w-md h-full overflow-y-auto">
            <div className="p-4 border-b sticky top-0 bg-white z-10">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">我的点餐</h2>
                <button onClick={toggleCart} className="text-gray-500">
                  ×
                </button>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                {formatDate(selectedDate)} {selectedMeal}
              </div>
            </div>

            {cartItems.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                购物车是空的
              </div>
            ) : (
              <div className="divide-y">
                {cartItems.map(item => (
                  <div key={item.id} className="p-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-12 h-12 rounded object-cover mr-3"
                      />
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(item.mealDate)} {item.mealTime}
                        </p>
                        <p className="text-blue-500">¥{item.price}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="bg-gray-200 text-gray-700 rounded-full p-1"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                      <span className="mx-2 w-8 text-center">{item.quantity}</span>
                      <button
                        onClick={() => addToCart(item)}
                        className="bg-blue-500 text-white rounded-full p-1"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {cartItems.length > 0 && (
              <div className="p-4 border-t sticky bottom-0 bg-white">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-bold">总计:</span>
                  <span className="text-blue-500 font-bold text-lg">¥{totalPrice}</span>
                </div>
                <button
                  className={`w-full py-3 rounded-lg font-bold transition-colors ${
                    orderMutation.isPending
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-500 hover:bg-blue-600'
                  } text-white`}
                  onClick={handleConfirmOrder}
                  disabled={orderMutation.isPending}
                >
                  {orderMutation.isPending ? '提交中...' : '确认点餐'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 分类导航 */}
      <div className="bg-white p-4 overflow-x-auto">
        <div className="flex space-x-4">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-full whitespace-nowrap ${
                activeCategory === category
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* 膳食列表 */}
      <div className="p-4 space-y-4">
        {isLoadingMeals ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-gray-600">加载餐品中...</p>
          </div>
        ) : meals_data.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            暂无餐品数据
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-bold text-lg">{selectedMeal}餐品</h3>
              <span className="text-sm text-gray-500">{activeCategory}</span>
            </div>

            <div className="space-y-3">
              {meals_data.map(dish => (
                <div key={dish.id} className="flex items-center justify-between p-2 border rounded-lg">
                  <div className="flex items-center">
                    <img
                      src={dish.image}
                      alt={dish.name}
                      className="w-12 h-12 rounded object-cover mr-3"
                    />
                    <div>
                      <p className="font-medium">{dish.name}</p>
                      <p className="text-xs text-gray-500 mb-1">{dish.description}</p>
                      <p className="text-blue-500 text-sm">¥{dish.price}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => addToCart(dish)}
                    className="bg-blue-500 text-white rounded-full p-1.5"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 购物车浮动按钮 - 修改为左下角 */}
      {cartItems.length > 0 && !showCart && (
        <div
          className="fixed bottom-20 left-6 bg-blue-500 text-white rounded-full p-4 shadow-lg flex items-center cursor-pointer"
          onClick={toggleCart}
        >
          <ShoppingCart className="h-6 w-6 mr-2" />
          <span className="font-bold">¥{totalPrice}</span>
        </div>
      )}

      {/* 在返回语句的最后，购物车浮动按钮后面添加底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-20">
        <div className="flex justify-around items-center h-16">
          <button
            onClick={handleBackToPatients}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <ArrowLeft className="h-6 w-6" />
            <span className="text-xs mt-1">病人列表</span>
          </button>

          <button
            onClick={toggleCart}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500 relative"
          >
            <ShoppingCart className="h-6 w-6" />
            <span className="text-xs mt-1">购物车</span>
            {cartItems.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
              </span>
            )}
          </button>

          <Link
            to={`/orders?patientId=${patientId}`}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <User className="h-6 w-6" />
            <span className="text-xs mt-1">订单记录</span>
          </Link>

          <button
            onClick={handleLogout}
            className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
          >
            <LogOut className="h-6 w-6" />
            <span className="text-xs mt-1">退出系统</span>
          </button>
        </div>
      </div>

      {/* 为底部导航腾出空间 */}
      <div className="h-16"></div>
    </div>
  );
};

export default Index;
