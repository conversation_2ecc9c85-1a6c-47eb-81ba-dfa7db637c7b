import oracledb from 'oracledb';
import 'dotenv/config';

oracledb.initOracleClient({libDir: process.env.ORACLE_CLIENT_PATH});

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECT_STRING
};

async function checkExisting() {
  let connection;
  
  try {
    connection = await oracledb.getConnection(dbConfig);
    console.log('Connected to database');
    
    // Check existing tables
    const tables = await connection.execute(
      'SELECT table_name FROM user_tables ORDER BY table_name',
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('Existing tables:', tables.rows.map(row => row.TABLE_NAME));
    
    // Check wards table data
    const wards = await connection.execute(
      'SELECT * FROM wards',
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('Wards data:', wards.rows);
    
    // Check doctors table data
    const doctors = await connection.execute(
      'SELECT * FROM doctors',
      {},
      { outFormat: oracledb.OUT_FORMAT_OBJECT }
    );
    
    console.log('Doctors data:', doctors.rows);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.close();
      console.log('Connection closed');
    }
  }
}

checkExisting();
