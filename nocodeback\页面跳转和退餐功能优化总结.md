# 医院营养点餐系统 - 页面跳转和退餐功能优化总结

## 🎯 优化目标

1. **页面跳转优化**：从 Index.jsx 跳转到 Orders.jsx 时传递病人ID，从 Orders.jsx 返回 Index.jsx 时保持病人ID
2. **订单筛选优化**：Orders.jsx 根据病人ID查询该病人的订单记录
3. **退餐功能**：在订单记录中添加退餐按钮，点击退餐时新增一条数量为负的记录到订单表

## 🔧 技术实现

### 1. 页面跳转优化

#### Index.jsx 修改
- **订单记录链接**：将原来的 `/orders` 改为 `/orders?patientId=${patientId}`
- **URL参数传递**：确保病人ID通过URL参数传递到订单页面

```jsx
<Link
  to={`/orders?patientId=${patientId}`}
  className="flex flex-col items-center justify-center text-gray-600 hover:text-blue-500"
>
  <User className="h-6 w-6" />
  <span className="text-xs mt-1">订单记录</span>
</Link>
```

#### Orders.jsx 修改
- **URL参数接收**：使用 `useSearchParams` 获取病人ID
- **返回导航**：添加返回按钮，保持病人ID参数

```jsx
const [searchParams] = useSearchParams();
const patientId = searchParams.get('patientId');

const handleBackToIndex = () => {
  if (patientId) {
    navigate(`/?patientId=${patientId}`);
  } else {
    navigate('/');
  }
};
```

### 2. 订单筛选优化

#### API查询优化
- **查询参数**：在订单查询API中添加 `patientId` 参数
- **数据库筛选**：后端根据病人ID筛选订单记录

```jsx
const params = new URLSearchParams();
if (activeTab && activeTab !== '全部') {
  params.append('status', activeTab);
}
if (patientId) {
  params.append('patientId', patientId);
}
```

#### 后端API支持
- **参数处理**：后端接收 `patientId` 参数并添加到SQL查询条件
- **数据筛选**：只返回指定病人的订单记录

```javascript
if (patientId) {
  query += ` AND patient_id = :patientId`;
  binds.patientId = patientId;
}
```

### 3. 退餐功能实现

#### 前端退餐UI
- **退餐按钮**：只在"已完成"状态的订单中显示退餐按钮
- **确认对话框**：点击退餐时显示确认对话框
- **加载状态**：退餐过程中显示加载状态

```jsx
{order.status === '已完成' && (
  <button
    onClick={() => handleRefund(order, item)}
    disabled={refundMutation.isPending}
    className={`px-3 py-1 rounded text-sm transition-colors ${
      refundMutation.isPending
        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
        : 'bg-red-500 hover:bg-red-600 text-white'
    }`}
  >
    {refundMutation.isPending ? '退餐中...' : '退餐'}
  </button>
)}
```

#### 退餐数据处理
- **负数数量**：退餐记录的数量设置为负数
- **特殊标记**：餐次标记为"退餐"，分类标记为"退餐"
- **完整信息**：包含原订单的所有病人和餐品信息

```jsx
const refundData = {
  patientId: patientInfo.patientId,
  patientName: patientInfo.patientName,
  wardName: patientInfo.wardName,
  bedNumber: patientInfo.bedNumber,
  items: [{
    id: item.mealId,
    name: item.name,
    description: '退餐: ' + item.name,
    price: item.price,
    image: item.image,
    category: '退餐',
    quantity: -item.quantity, // 负数表示退餐
    mealDate: new Date().toISOString(),
    mealTime: '退餐',
  }],
  totalPrice: -item.price * item.quantity // 负数表示退款
};
```

#### 后端退餐处理
- **复用订单API**：退餐使用相同的订单创建API
- **数据库记录**：退餐记录保存到同一个 `meal_orders` 表
- **负数金额**：退餐记录的金额为负数

## 📊 数据流程

### 页面跳转流程
1. **订餐页面** → 点击"订单记录" → **订单页面**（带病人ID）
2. **订单页面** → 点击返回按钮 → **订餐页面**（保持病人ID）

### 订单查询流程
1. **前端请求**：`GET /api/orders?patientId=1&status=已完成`
2. **后端筛选**：根据病人ID和状态筛选数据库记录
3. **数据返回**：只返回指定病人的订单

### 退餐流程
1. **用户操作**：点击"已完成"订单中的"退餐"按钮
2. **确认对话框**：显示退餐确认信息
3. **数据提交**：`POST /api/orders`（退餐数据，数量为负）
4. **数据库保存**：新增退餐记录到 `meal_orders` 表
5. **页面刷新**：重新加载订单列表

## ✅ 功能验证

### 页面跳转测试
- ✅ **Index → Orders**：URL正确传递病人ID参数
- ✅ **Orders → Index**：返回时保持病人ID参数
- ✅ **URL参数**：`/orders?patientId=1` 格式正确

### 订单筛选测试
- ✅ **病人筛选**：只显示指定病人的订单
- ✅ **状态筛选**：支持按订单状态筛选
- ✅ **组合筛选**：病人ID + 状态的组合筛选

### 退餐功能测试
- ✅ **按钮显示**：只在"已完成"订单中显示退餐按钮
- ✅ **确认对话框**：点击退餐时正确显示确认信息
- ✅ **数据提交**：退餐数据正确提交到后端
- ✅ **数据库记录**：退餐记录正确保存（负数数量）

## 🎉 优化成果

### 用户体验提升
- **无缝跳转**：页面间跳转保持上下文（病人ID）
- **精准查询**：只显示当前病人的订单记录
- **便捷退餐**：一键退餐，操作简单直观

### 数据一致性
- **完整记录**：退餐记录包含完整的病人和餐品信息
- **负数标记**：通过负数数量清晰标识退餐记录
- **审计追踪**：所有操作都有完整的数据库记录

### 系统架构
- **统一API**：退餐复用订单创建API，减少代码重复
- **数据模型**：单表设计支持正常订单和退餐记录
- **状态管理**：React Query 自动处理数据刷新

## 🔄 数据示例

### 正常订单记录
```json
{
  "id": "1749291915893_101_1749291915893_q46tvez",
  "orderId": "1749291915893",
  "patientId": "1",
  "patientName": "张三",
  "mealName": "营养粥套餐",
  "quantity": 1,
  "unitPrice": 15,
  "totalAmount": 15,
  "status": "已完成",
  "mealTime": "早餐"
}
```

### 退餐记录
```json
{
  "id": "1749292100000_101_1749292100000_refund123",
  "orderId": "1749292100000",
  "patientId": "1",
  "patientName": "张三",
  "mealName": "营养粥套餐",
  "quantity": -1,        // 负数表示退餐
  "unitPrice": 15,
  "totalAmount": -15,    // 负数表示退款
  "status": "待处理",
  "mealTime": "退餐",    // 特殊标记
  "description": "退餐: 营养粥套餐"
}
```

## 🚀 后续扩展

### 功能扩展
- **退餐审批**：添加退餐审批流程
- **退餐统计**：统计退餐率和退餐原因
- **批量退餐**：支持一次退订整个订单
- **退餐时限**：设置退餐时间限制

### 用户体验
- **退餐原因**：添加退餐原因选择
- **退餐通知**：退餐成功后的消息通知
- **退餐历史**：查看退餐历史记录
- **快速退餐**：常用退餐原因快捷选择

现在整个医院营养点餐系统已经实现了完整的页面跳转逻辑和退餐功能，用户可以在不同页面间无缝切换，并且可以方便地进行退餐操作！🎊
