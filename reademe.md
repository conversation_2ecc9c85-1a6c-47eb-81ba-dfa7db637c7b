
 cd nocodeback
 node server.js

 cd nocode
 npm run dev


当前项目为前后端分离的订餐系统，
nocode为前端，http://localhost:8080
nocodeback为后端，http://localhost:5000
前端现在为mock模拟数据，需改为调用后端api。
请仔细分析前后端项目结构内容。逐个接口进行改造测试。


根据API文档，后端提供以下主要接口：

1. 认证接口
   - POST /api/auth/login - 用户登录
2. 病区管理
   - GET /api/wards - 获取病区列表
   - GET /api/wards/:wardId - 获取病区详情
3. 病人管理
   - GET /api/wards/:wardId/patients - 获取病区病人列表
4. 餐品管理
   - GET /api/meal/categories - 获取餐品分类
   - GET /api/meals - 获取餐品列表
5. 订单管理
   - POST /api/orders - 提交订单
   - GET /api/orders - 获取订单列表
6. 统计报表
   - GET /api/stats/orders - 获取订单统计


POST /api/auth/login - 用户登录
1. 病区列表页面 - 检查是否调用 /api/wards
2. 病人管理页面 - 检查是否调用 /api/wards/:wardId/patients
3. 餐品页面 - 检查是否调用 /api/meals 和 /api/meal/categories
4. 订单页面 - 检查是否调用 /api/orders
5. 统计页面 - 检查是否调用 /api/stats/orders
## 注意事项
1. 认证机制 ：除了登录接口，其他接口都需要JWT token认证
2. 代理配置 ：前端已正确配置代理， /api 请求会自动转发到后端
3. 数据库 ：后端使用Oracle数据库，需要确保数据库连接正常